import type { SubscribeUpdateAccountInfo } from '@kdt-sol/geyser-client'
import { TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import { type Account, address, lamports } from '@solana/kit'
import base58 from 'bs58'

export const toSolanaAccount = (account: SubscribeUpdateAccountInfo, program: string = TOKEN_PROGRAM_ADDRESS): Account<Uint8Array> => ({
    programAddress: address(program),
    address: address(base58.encode(account.pubkey)),
    lamports: lamports(BigInt(account.lamports)),
    executable: account.executable,
    data: account.data,
    space: 0n,
})
