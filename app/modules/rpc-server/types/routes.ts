import type { RpcMeth<PERSON><PERSON>andler } from '@kdt310722/rpc'
import type { AnyObject } from '@kdt310722/utils/object'
import type { ZodTypeAny } from 'zod'
import type { WebsocketRpcMethodHandler } from '../websocket'

export interface BaseRoute {
    name?: string
    schema?: ZodTypeAny
}

export interface HttpRoute<TContext extends AnyObject = AnyObject> extends BaseRoute {
    httpHandler: RpcMethodHandler<TContext>
}

export interface WebSocketRoute<TContext extends AnyObject = AnyObject> extends BaseRoute {
    wsHandler: WebsocketRpcMethodHandler<TContext>
}

export interface HttpAndWebSocketRoute<TContext extends AnyObject = AnyObject> extends BaseRoute {
    httpHandler: RpcMethodHandler<TContext>
    wsHandler: WebsocketRpcMethodHandler<TContext>
}

export interface RouteWithOneHandler<TContext extends AnyObject = AnyObject> extends BaseRoute {
    handler: Rpc<PERSON>ethod<PERSON><PERSON><PERSON><TContext>
}

export type Route<TContext extends AnyObject = AnyObject> = HttpRoute<TContext> | WebSocketRoute<TContext> | HttpAndWebSocketRoute<TContext> | RouteWithOneHandler<TContext>
