import type { BuyToken, ConfirmResultError, CreateBuyTransactionParams } from '../common/buyer'
import type { Token } from '../modules/indexer/types/entities/token'
import { type Address, type Signature, signature, type TransactionError } from '@solana/kit'
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { BigIntColumn } from '../utils/database/columns/bigint-column'
import { JsonColumn } from '../utils/database/columns/json-column'

export interface BuyFailedItem {
    sender: string
    slot: number
    signature: Signature
    err: TransactionError
    sendTime: bigint
    confirmationTime: number
}

@Entity()
export class BuyFailedTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column('varchar')
    public declare mint: Address

    @JsonColumn()
    public declare tokenInfo: Token

    @BigIntColumn()
    public declare amount: bigint

    @Column()
    public declare slippage: number

    @BigIntColumn()
    public declare priorityFee: bigint

    @BigIntColumn()
    public declare tip: bigint

    @BigIntColumn()
    public declare processTime: bigint

    @JsonColumn()
    public declare items: BuyFailedItem[]

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date

    public static fromBuyResult(token: BuyToken, result: ConfirmResultError[], processTime: bigint, params: CreateBuyTransactionParams) {
        const entity = new BuyFailedTransaction()

        entity.mint = token.mint
        entity.tokenInfo = token.info
        entity.amount = params.amount
        entity.slippage = params.slippage
        entity.priorityFee = params.priorityFee
        entity.tip = params.tip
        entity.processTime = processTime

        entity.items = result.map((item): BuyFailedItem => ({
            sender: item.sender.name,
            slot: item.slot,
            signature: signature(item.signature),
            err: item.err,
            sendTime: item.sendTime,
            confirmationTime: item.took,
        }))

        return entity
    }
}
