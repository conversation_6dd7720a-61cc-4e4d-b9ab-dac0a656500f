import type { WalletDatasourceTransaction } from '../../modules/wallet/types'
import type { LoadedAddresses } from '../../types/transactions'
import { type Address, type Lamports, signature, type TokenBalance } from '@solana/kit'
import { getTransactionAccounts, type GetTransactionResponse } from '../rpc-client/transactions'

export const toWalletDatasourceTransaction = (signature_: string, tx: GetTransactionResponse): WalletDatasourceTransaction => {
    const accounts = getTransactionAccounts(tx.transaction.message.accountKeys as Address[], tx.transaction.message.header, tx.meta?.loadedAddresses as LoadedAddresses)
    const instructions = [...tx.transaction.message.instructions, ...(tx.meta?.innerInstructions ?? []).flatMap((i) => i.instructions)]

    return { slot: Number(tx.slot), accounts, signature: signature(signature_), logs: (tx.meta?.logMessages ?? []) as string[], instructions, preBalances: (tx.meta?.preBalances ?? []) as Lamports[], postBalances: (tx.meta?.postBalances ?? []) as Lamports[], preTokenBalances: tx.meta?.preTokenBalances as TokenBalance[], postTokenBalances: tx.meta?.postTokenBalances as TokenBalance[] }
}
