import { serialize } from 'node:v8'
import { sum } from '@kdt310722/utils/array'
import { formatBytes } from '@kdt310722/utils/number'
import { type AnyObject, map } from '@kdt310722/utils/object'
import { aggregator } from '../common/aggregator'
import { availableToBuyTokens } from '../common/buyer'
import { removedTokens } from '../common/cleaner'
import { getServerStats } from '../common/monitor'

export function formatMemoryUsage(obj: AnyObject) {
    return map(obj, (key, value) => [key, formatBytes(value)])
}

export function getClassMemoryUsage(obj: AnyObject, keys: string[]) {
    const memories = Object.fromEntries(keys.map((key) => [key, serialize(obj[key]).byteLength]))
    const total = formatBytes(sum(Object.values(memories)))

    return { total, ...map(memories, (key, value) => [key, formatBytes(value)]) }
}

export const handler = () => {
    const stats = getServerStats()
    const memory = formatMemoryUsage(process.memoryUsage())
    const aggregator_ = getClassMemoryUsage(aggregator, ['pendingTrades', 'ignoredTrades', 'handledTradeIds', 'ignoredTradeIds', 'items', 'mintsByCreator', 'mintsByTrader'])
    const cleaner = { tokens: removedTokens.size, memory: formatBytes(serialize(removedTokens).byteLength) }
    const tokensCount = Object.keys(aggregator.items).length

    return { availableToBuyTokens: availableToBuyTokens.size, tokensCount, stats, memory, aggregator: aggregator_, cleaner }
}
