import type { IndexerClient } from '../indexer-client'
import type { Token, TokenApiResponse } from '../types/entities/token'
import type { GetParams } from '../types/params'
import { formatToken } from '../utils/formatters'

export async function * getTokens(client: IndexerClient, { limit = 1000, ...params }: GetParams = {}, offset = 0): AsyncGenerator<Token[]> {
    const tokens = await client.call<TokenApiResponse[]>('getTokens', { ...params, count: false, limit, offset, sortDirection: 'ASC' }).then((tokens) => tokens.map(formatToken))

    if (tokens.length === 0) {
        return
    }

    yield tokens

    if (tokens.length >= limit) {
        yield* getTokens(client, { ...params, limit }, offset + limit)
    }
}

export async function toArray(iterator: AsyncGenerator<Token[]>) {
    const tokens: Token[] = []

    for await (const chunk of iterator) {
        tokens.push(...chunk)
    }

    return tokens
}

export async function getTokensCount(client: IndexerClient, params: Omit<GetParams, 'limit'> = {}) {
    return client.call<{ total: number, tokens: TokenApiResponse[] }>('getTokens', { ...params, count: true, limit: 1, offset: 0, sortDirection: 'ASC' }).then(({ total }) => total)
}
