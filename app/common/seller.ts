import type { SellConfig } from '../config/sell'
import type { BuyTransaction } from '../entities/buy-transaction'
import { highlight } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { BigIntMath } from '@kdt310722/utils/number'
import { formatDate } from '@kdt310722/utils/time'
import { config } from '../config'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { SellOrder } from '../entities/sell-order'
import { Seller } from '../modules/seller/seller'
import { formatSol } from '../utils/formatters/format-sol'
import { buyer } from './buyer'
import { sell } from './sell'

export const logger = createChildLogger('common:seller')
export const sellingTokens = new Set<string>()

export async function createSeller(order: SellOrder) {
    const token = highlight(order.buyTransaction.tokenInfo.symbol)
    const timer = tap(logger.createTimer(), () => logger.info(`Creating seller for token ${token}...`))
    const seller = new Seller(order)
    const cost = BigIntMath.abs(order.buyTransaction.balanceAfter - order.buyTransaction.balanceBefore)

    seller.on('error', (error) => logger.forceExit(1, 'fatal', `Seller for token ${token} error`, error, { order }))
    seller.on('holdingTime', (date) => logger.info(`Auto sell token ${token} at: ${highlight(formatDate(date))}`))
    seller.on('tpAndSl', (tp, sl, id, idCount) => logger.info(`TP And SL for token ${token} updated: ${highlight(formatSol(tp))} / ${highlight(formatSol(sl))}${notNullish(id) && notNullish(idCount) ? ` (ID: ${highlight(id)} - ${highlight(idCount)})` : ''}`))

    seller.on('stopped', (reason) => {
        sellingTokens.delete(order.mint)
        seller.removeAllListeners()
        logger.info(`Seller for token ${token} stopped: ${highlight(reason)}`)
    })

    seller.on('holding', (holding) => {
        const totalFee = order.config.priorityFee + order.config.tip
        const pnl = (holding + totalFee) - cost

        logger.info(`Current holding for token ${token}: ${highlight(formatSol(holding))} (PnL: ${highlight(formatSol(pnl))})`)
    })

    await seller.start().then(() => {
        if (!seller.isStopped) {
            sellingTokens.add(order.mint)
            logger.stopTimer(timer, 'info', `Auto sell for token ${token} has been started!`)
        }
    })
}

export async function createSellOrder(buyTransaction: BuyTransaction, config: SellConfig) {
    return createSeller(await database.getRepository(SellOrder).save(SellOrder.fromBuyTransaction(buyTransaction, config)))
}

buyer.on('bought', (entity) => config.sell?.enabled && createSellOrder(entity, config.sell).catch((error) => {
    logger.error(error, `Failed to create sell order for token ${highlight(entity.mint)}, immediate selling...`, error)
    Promise.resolve().then(() => sell(entity.tokenInfo, config.sell!))
}))

export async function initializeSeller() {
    const orders = await database.getRepository(SellOrder).find({ where: { isCompleted: false }, relations: { buyTransaction: true } })

    for (const order of orders) {
        await createSeller(order)
    }
}
