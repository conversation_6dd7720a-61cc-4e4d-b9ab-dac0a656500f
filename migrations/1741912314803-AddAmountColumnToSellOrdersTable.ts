import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddAmountColumnToSellOrdersTable1741912314803 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sell_orders" ADD "amount" bigint NOT NULL`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sell_orders" DROP COLUMN "amount"`)
    }
}
