import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateBuyFailedTransactionsTable1741728069288 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "buy_failed_transactions" ("id" SERIAL NOT NULL, "mint" character varying NOT NULL, "token_info" json NOT NULL, "amount" bigint NOT NULL, "slippage" integer NOT NULL, "priority_fee" bigint NOT NULL, "tip" bigint NOT NULL, "process_time" bigint NOT NULL, "items" json NOT NULL, CONSTRAINT "PK_b920ca899450bcdf8ef5908513e" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_511a92cb5c43466f97995d5e00" ON "buy_failed_transactions" ("mint") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_511a92cb5c43466f97995d5e00"`)
        await queryRunner.query(`DROP TABLE "buy_failed_transactions"`)
    }
}
