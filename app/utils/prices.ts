import { Big } from 'big.js'

export function calculatePercentDrop(ath: bigint, price: bigint) {
    if (price >= ath) {
        return 0
    }

    return Number((Number((ath - price) * 100n) / Number(ath)).toFixed(2))
}

export function calculatePercentIncrease(atl: bigint, price: bigint) {
    if (price <= atl) {
        return 0
    }

    return Number((Number((price - atl) * 100n) / Number(atl)).toFixed(2))
}

export function calculatePercentDifference(a: bigint, b: bigint) {
    if (a === b) {
        return 0
    }

    return Math.abs(Number((Number((a - b) * 100n) / Number(b)).toFixed(2)))
}

export function calculateProfitPercentage(cost: bigint, profit: bigint) {
    if (cost === 0n) {
        return 0
    }

    return new Big(profit.toString()).div(cost.toString()).mul(100).toNumber()
}
