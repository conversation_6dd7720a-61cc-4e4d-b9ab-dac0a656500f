import type { GeyserClient, SubscribeUpdate } from '@kdt-sol/geyser-client'
import type { Address } from '@solana/kit'
import type { WalletDatasource, WalletDatasourceEvents } from '../../modules/wallet/types'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import { getTokenAccountGeyserFilters } from '../token-accounts'
import { toSolanaAccount } from './utils/accounts'
import { toWalletDatasourceTransaction } from './utils/transactions'

export class GeyserWalletDatasource extends Emitter<WalletDatasourceEvents, true> implements WalletDatasource {
    protected accountSubscriptionId?: string
    protected tokenAccountSubscriptionId?: string
    protected transactionSubscriptionId?: string

    public constructor(protected readonly client: GeyserClient, protected readonly wallet: Address) {
        super()

        client.on('data', (subscriptionId, data) => {
            this.handleData(subscriptionId, data)
        })
    }

    public async subscribe() {
        await this.subscribeAccount()
        await this.subscribeTokenAccount()
        await this.subscribeTransaction()
    }

    public async subscribeAccount() {
        this.accountSubscriptionId = await this.client.subscribe('accounts', { account: [this.wallet], owner: [], filters: [] })
    }

    public async subscribeTokenAccount() {
        this.tokenAccountSubscriptionId = await this.client.subscribe('accounts', { account: [], owner: [TOKEN_PROGRAM_ADDRESS], filters: getTokenAccountGeyserFilters(this.wallet) })
    }

    public async subscribeTransaction() {
        this.transactionSubscriptionId = await this.client.subscribe('transactions', { failed: false, vote: false, accountRequired: [this.wallet], accountInclude: [], accountExclude: [] })
    }

    protected handleAccount(data: NonNullable<SubscribeUpdate['account']>) {
        if (notNullish(data.account)) {
            this.emit('account', this.wallet, toSolanaAccount(data.account), { slot: Number(data.slot) })
        }
    }

    protected handleTokenAccount(data: NonNullable<SubscribeUpdate['account']>) {
        if (notNullish(data.account)) {
            this.emit('tokenAccount', this.wallet, toSolanaAccount(data.account), { slot: Number(data.slot) })
        }
    }

    protected handleTransaction(data: NonNullable<SubscribeUpdate['transaction']>) {
        if (notNullish(data.transaction) && isNullish(data.transaction.meta?.err) && notNullish(data.transaction.meta?.logMessages)) {
            this.emit('transaction', this.wallet, toWalletDatasourceTransaction(Number(data.slot), data.transaction), { slot: Number(data.slot) })
        }
    }

    protected handleData(subscriptionId: string, data: SubscribeUpdate) {
        if (subscriptionId === this.accountSubscriptionId && notNullish(data.account)) {
            this.handleAccount(data.account)
        } else if (subscriptionId === this.tokenAccountSubscriptionId && notNullish(data.account)) {
            this.handleTokenAccount(data.account)
        } else if (subscriptionId === this.transactionSubscriptionId && notNullish(data.transaction)) {
            this.handleTransaction(data.transaction)
        }
    }
}
