import type { Url<PERSON><PERSON> } from '@kdt310722/rpc'
import type { SenderOptions } from './sender'
import { ZERO_SLOT_ENDPOINTS, ZERO_SLOT_MINIMUM_TIP_AMOUNT, ZERO_SLOT_TIP_ACCOUNTS } from '../constants'
import { RpcSender } from './rpc'
import { formatEndpoints } from './temporal'

export interface ZeroSlotSenderOptions<TEndpoints extends Record<string, UrlLike> = typeof ZERO_SLOT_ENDPOINTS> extends SenderOptions {
    endpoints?: TEndpoints
}

export class ZeroSlotSender<TEndpoints extends Record<string, UrlLike> = typeof ZERO_SLOT_ENDPOINTS> extends Rpc<PERSON>ender {
    public override readonly name = '0Slot'
    public override readonly tipWallets = ZERO_SLOT_TIP_ACCOUNTS
    public override readonly minimumTipAmount = ZERO_SLOT_MINIMUM_TIP_AMOUNT

    public constructor(endpoint: Extract<keyof TEndpoints, string>, api<PERSON><PERSON>: string, { endpoints, ...options }: ZeroSlotSenderOptions<TEndpoints> = {}) {
        super(formatEndpoints(endpoints ?? ZERO_SLOT_ENDPOINTS, apiKey, 'api-key'), endpoint, options)
    }
}
