import type { AggregatedTokenStats } from '../modules/aggregator/types'
import type { TokenStats } from '../modules/indexer/types/entities/token-stats'
import { isNullish, type Nullable } from '@kdt310722/utils/common'
import { map, pick } from '@kdt310722/utils/object'
import { aggregator } from '../common/aggregator'
import { indexer } from '../common/indexer'

export const keys = <const>['sortKey', 'signature', 'atl', 'ath', 'buyVolume', 'sellVolume', 'buysCount', 'sellsCount']

export function isStatsEqual(local: Nullable<AggregatedTokenStats>, server: Nullable<TokenStats>) {
    if (isNullish(local)) {
        return isNullish(server)
    }

    if (isNullish(server)) {
        return false
    }

    return keys.every((key) => local[key] === server[key])
}

interface DiffResult {
    mint: string
    local: Record<typeof keys[number], string> | null
    server: Record<typeof keys[number], string> | null
}

export function toDiffResult(stats: Nullable<AggregatedTokenStats | TokenStats>) {
    if (isNullish(stats)) {
        return null
    }

    return map(pick(stats, ...keys), (key, value) => [key, value.toString()]) as Record<typeof keys[number], string>
}

export const handler = async () => {
    const items = Object.freeze({ ...aggregator['items'] })
    const diffs: DiffResult[] = []

    for (const [mint, item] of Object.entries(items)) {
        if (isNullish(item.stats?.sortKey)) {
            continue
        }

        const endSortKey = item.stats.sortKey
        const serverStats = await indexer.getStats([mint], { endSortKey }).then((stats) => stats[0])

        if (item.stats?.sortKey === serverStats?.sortKey && !isStatsEqual(item.stats, serverStats)) {
            diffs.push({ mint, local: toDiffResult(item.stats), server: toDiffResult(serverStats) })
        }
    }

    return diffs
}
