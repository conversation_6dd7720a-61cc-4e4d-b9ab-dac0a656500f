import type { Address } from '@solana/kit'
import type { SellConfig } from '../config/sell'
import { BigIntMath } from '@kdt310722/utils/number'
import { Column, CreateDateColumn, Entity, Index, JoinColumn, OneToOne, PrimaryGeneratedColumn, type Relation, UpdateDateColumn } from 'typeorm'
import { computeStopLoss, computeTargetProfit } from '../modules/seller/utils'
import { BigIntColumn } from '../utils/database/columns/bigint-column'
import { JsonColumn } from '../utils/database/columns/json-column'
import { BuyTransaction } from './buy-transaction'
import { SellTransaction } from './sell-transaction'

@Entity()
export class SellOrder {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column('varchar')
    public declare mint: Address

    @OneToOne(() => BuyTransaction)
    @JoinColumn()
    public declare buyTransaction: Relation<BuyTransaction>

    @OneToOne(() => SellTransaction, { nullable: true })
    @JoinColumn()
    public declare sellTransaction?: Relation<SellTransaction>

    @BigIntColumn()
    public declare amount: bigint

    @JsonColumn()
    public declare config: SellConfig

    @Column()
    public declare isCompleted: boolean

    @Column()
    public declare tpCount: number

    @BigIntColumn()
    public declare tp: bigint

    @BigIntColumn()
    public declare sl: bigint

    @Column('timestamptz')
    public declare holdUntil: Date

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date

    public static fromBuyTransaction(buyTransaction: BuyTransaction, config: SellConfig) {
        const { holding: { withoutTrades }, tp, sl } = config

        const entity = new SellOrder()
        const amount = BigIntMath.abs(buyTransaction.balanceAfter - buyTransaction.balanceBefore)

        entity.mint = buyTransaction.mint
        entity.buyTransaction = buyTransaction
        entity.amount = amount
        entity.config = config
        entity.isCompleted = false
        entity.tpCount = 0

        entity.tp = computeTargetProfit(amount, tp)
        entity.sl = computeStopLoss(amount, sl)
        entity.holdUntil = new Date(Date.now() + withoutTrades)

        return entity
    }
}
