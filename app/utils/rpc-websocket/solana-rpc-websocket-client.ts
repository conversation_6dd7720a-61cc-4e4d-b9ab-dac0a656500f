import type { Commitment, TransactionError } from '@solana/kit'
import type { ConfirmTransactionOptions, ConfirmTransactionResult } from '../../types/transactions'
import { RpcWebSocketClient } from '@kdt310722/rpc'
import { isNullish, notNullish, type Nullable } from '@kdt310722/utils/common'
import { isNumber } from '@kdt310722/utils/number'
import { isKeysOf, isObject } from '@kdt310722/utils/object'
import { abortable, createDeferred, type DeferredPromise, tap, withTimeout } from '@kdt310722/utils/promise'
import { onAborted } from '../abort'

export interface RpcWebsocketConfirmTransactionOptions extends ConfirmTransactionOptions {
    commitment?: Commitment
}

export interface Subscription<TData = any> {
    method: string
    params: unknown
    unsubscribeMethod: string
    subscriptionId: number
    onData: (data: TData) => void
}

export class SolanaRpcWebsocketClient extends RpcWebSocketClient {
    protected readonly slotHandlers = new Set<(slot: number) => void>()
    protected readonly latencyHandlers = new Set<(latency: number) => void>()

    protected subscriptions: Record<string, Subscription> = {}
    protected subscriptionIds: Record<number, string> = {}

    protected id = 0
    protected slotSubscription?: Promise<string>
    protected slotSubscriptionId?: string
    protected hasSlot?: DeferredPromise<void>

    protected latencySubscription?: Promise<string>
    protected latencySubscriptionId?: string

    #latestSlot?: number
    #latency?: number

    public constructor(...args: ConstructorParameters<typeof RpcWebSocketClient>) {
        super(...args)

        this.on('notification', (method, params) => this.handleNotification(method, params))
        this.socket.on('disconnected', (_, __, isExplicitlyClosed) => isExplicitlyClosed && this.reset())
    }

    public get latestSlot() {
        return this.#latestSlot!
    }

    public get latency() {
        return this.#latency!
    }

    public onSlot(handler: (slot: number) => void) {
        this.slotHandlers.add(handler)
    }

    public onLatency(handler: (latency: number) => void) {
        this.latencyHandlers.add(handler)
    }

    public async confirmTransaction(signature: string, { commitment = 'confirmed', maxWaitTime = 60 * 1000, signal }: RpcWebsocketConfirmTransactionOptions = {}) {
        const result = createDeferred<ConfirmTransactionResult>()
        const stopAbort = onAborted((error) => !result.isSettled && result.reject(error), signal)

        const handleData = (data: { context: { slot: number }, value: { err: Nullable<TransactionError> } }) => {
            if (!result.isSettled) {
                result.resolve({ slot: data.context.slot, signature, err: data.value.err ?? undefined })
            }
        }

        const subscriptionId = await abortable(this.subscribe('signatureSubscribe', [signature, { commitment }], 'signatureUnsubscribe', handleData), signal).catch((error) => {
            if (!result.isSettled) {
                result.reject(error)
            }

            return null
        })

        return withTimeout(result, maxWaitTime, 'Transaction confirmation timeout').finally(() => {
            stopAbort()

            if (notNullish(subscriptionId)) {
                this.unsubscribe(subscriptionId, true)
            }
        })
    }

    public async subscribeLatency() {
        const handleSlotsUpdates = ({ type, timestamp }: { timestamp: number, type: string }) => {
            const now = Date.now()

            if (type === 'firstShredReceived' && now > timestamp) {
                this.#latency = now - timestamp

                for (const handler of this.latencyHandlers) {
                    handler(this.#latency)
                }
            }
        }

        return this.latencySubscription ??= this.subscribe('slotsUpdatesSubscribe', undefined, 'slotsUpdatesUnsubscribe', handleSlotsUpdates).then((id) => this.latencySubscriptionId = id)
    }

    public async subscribeSlot() {
        const hasSlot = this.hasSlot ??= createDeferred()

        const handleSlot = ({ slot }: { slot: number }) => {
            for (const handler of this.slotHandlers) {
                handler(slot)
            }

            if (isNullish(this.#latestSlot) || slot > this.#latestSlot) {
                this.#latestSlot = slot
            }

            if (!hasSlot.isSettled) {
                hasSlot.resolve()
            }
        }

        return this.slotSubscription ??= this.subscribe('slotSubscribe', undefined, 'slotUnsubscribe', handleSlot).then((id) => this.slotSubscriptionId = id).then(tap(() => hasSlot))
    }

    public async unsubscribeSlot() {
        if (notNullish(this.slotSubscriptionId)) {
            await this.unsubscribe(this.slotSubscriptionId).then(() => {
                this.slotSubscription = undefined
                this.slotSubscriptionId = undefined
            })
        }
    }

    public async resubscribe(beforeSubscribe?: (subscriptions: Subscription[]) => void) {
        const subscriptions = Object.values(this.subscriptions)

        if (subscriptions.length === 0) {
            return subscriptions
        }

        beforeSubscribe?.(subscriptions)

        for (const [id, { method, params, subscriptionId }] of Object.entries(this.subscriptions)) {
            await this.call<number>(method, params).then(tap(() => delete this.subscriptionIds[subscriptionId])).then((newSubscriptionId) => {
                this.subscriptionIds[newSubscriptionId] = id
                this.subscriptions[id].subscriptionId = newSubscriptionId
            })
        }

        return subscriptions
    }

    public async subscribe<TData>(method: string, params: unknown, unsubscribeMethod: string, onData: (data: TData) => void) {
        const id = String(++this.id)
        const subscriptionId = await this.call(method, params)

        this.subscriptions[id] = { method, params, subscriptionId, unsubscribeMethod, onData }
        this.subscriptionIds[subscriptionId] = id

        return id
    }

    public async unsubscribe(id: string, force = false) {
        const { subscriptionId, unsubscribeMethod } = this.subscriptions[id]

        delete this.subscriptions[id]
        delete this.subscriptionIds[subscriptionId]

        if (!force) {
            await this.call(unsubscribeMethod, [subscriptionId]).catch((error) => this.socket.emit('error', error))
        }
    }

    protected handleNotification(method: string, params: unknown) {
        if (isObject(params) && isKeysOf(params, ['subscription', 'result']) && isNumber(params.subscription) && notNullish(this.subscriptionIds[params.subscription])) {
            const id = this.subscriptionIds[params.subscription]
            const { onData } = this.subscriptions[id]

            return onData(params.result)
        }

        if (method !== 'signatureNotification') {
            this.emit('unhandledMessage', JSON.stringify({ method, params }))
        }
    }

    protected reset() {
        this.#latestSlot = undefined
        this.#latency = undefined
        this.hasSlot = undefined
        this.slotSubscription = undefined
        this.slotSubscriptionId = undefined
        this.latencySubscription = undefined
        this.latencySubscriptionId = undefined
        this.subscriptions = {}
        this.subscriptionIds = {}
    }
}
