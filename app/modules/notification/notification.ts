import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { BuyTransaction } from '../../entities/buy-transaction'
import { type BuyNotificationData, getBuyNotificationMessage } from './templates/buy'
import { getNftyBuyNotificationMessage } from './templates/nfty-buy'
import { getSellNotificationMessage, type SellNotificationData } from './templates/sell'
import { getPaymentTransactionNotificationMessage, type PaymentTransactionNotificationData } from './templates/transaction'
import { type NtfyNotification, send, sendNtfyNotification } from './utils/send'

export interface NotificationConfig {
    name?: string
    botToken?: string
    transaction?: string
    trade?: string
    buy?: string
    sell?: string
    ntfy?: string
}

export type NotificationEvents = {
    error: (error: unknown) => void
}

export class Notification extends Emitter<NotificationEvents, true> {
    protected readonly name?: string
    protected readonly botToken?: string
    protected readonly transactionChatId?: string
    protected readonly buyChatId?: string
    protected readonly sellChatId?: string
    protected readonly ntfyTopic?: string

    public constructor({ name, botToken, transaction, trade, buy, sell, ntfy }: NotificationConfig) {
        super()

        this.name = name
        this.botToken = botToken
        this.transactionChatId = transaction
        this.buyChatId = buy ?? trade
        this.sellChatId = sell ?? this.buyChatId
        this.ntfyTopic = ntfy
    }

    public sendPaymentTransactionNotification(data: PaymentTransactionNotificationData) {
        if (notNullish(this.transactionChatId)) {
            this.send(this.transactionChatId, getPaymentTransactionNotificationMessage(data))
        }
    }

    public sendBuyNotification(data: BuyNotificationData) {
        if (data.transaction instanceof BuyTransaction) {
            this.sendNtfy(getNftyBuyNotificationMessage(data))
        }

        if (notNullish(this.buyChatId)) {
            this.send(this.buyChatId, getBuyNotificationMessage(data, this.name))
        }
    }

    public sendSellNotification(data: SellNotificationData) {
        if (notNullish(this.sellChatId)) {
            this.send(this.sellChatId, getSellNotificationMessage(data))
        }
    }

    protected send(chatId: string, message: string) {
        if (notNullish(this.botToken)) {
            send(this.botToken, chatId, message).catch((error) => this.emit('error', error))
        }
    }

    protected sendNtfy({ message, link, title, priority = 5 }: NtfyNotification & { message: string }) {
        if (notNullish(this.ntfyTopic)) {
            sendNtfyNotification(this.ntfyTopic, message, { link, title, priority }).catch((error) => this.emit('error', error))
        }
    }
}
