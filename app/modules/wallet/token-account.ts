import type { Account, Address } from '@solana/kit'
import type { TokenAccount as TokenAccountType, WalletDatasourceTransaction } from './types'
import { Emitter } from '@kdt310722/utils/event'
import { transform } from '@kdt310722/utils/function'
import { isKeyOf, pick } from '@kdt310722/utils/object'
import { decodeToken, TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import { rpcClient } from '../../core/rpc-client'
import { getCloseTokenAccountInstructions } from './utils/accounts'

export type TokenAccountEvents = {
    load: (tokenAccounts: TokenAccountType[]) => void
    add: (tokenAccount: TokenAccountType) => void
    update: (tokenAccount: TokenAccountType) => void
    remove: (tokenAccount: TokenAccountType) => void
    error: (error: unknown) => void
}

export class TokenAccount extends Emitter<TokenAccountEvents, true> {
    protected readonly accounts: Record<string, TokenAccountType> = {}
    protected readonly mints: Record<string, string> = {}

    public constructor(protected readonly wallet: Address) {
        super()
    }

    public get(mint: Address) {
        return this.accounts[mint]
    }

    public has(address: Address) {
        return isKeyOf(this.mints, address)
    }

    public hasForMint(mint: Address) {
        return isKeyOf(this.accounts, mint)
    }

    public async load() {
        const { value } = await rpcClient.getTokenAccountsByOwner(this.wallet, { programId: TOKEN_PROGRAM_ADDRESS }, { commitment: 'confirmed', encoding: 'base64' }).send()

        for (const { pubkey, account } of value) {
            this.handleAccountUpdate({ ...account, data: Buffer.from(account.data[0], 'base64'), programAddress: TOKEN_PROGRAM_ADDRESS, address: pubkey }, false)
        }

        this.emit('load', Object.values(this.accounts))
    }

    public handleAccountUpdate(account: Account<Uint8Array>, emit = true) {
        const decoded = this.decodeTokenAccount(account)
        const isExisting = isKeyOf(this.accounts, decoded.mint)

        this.accounts[decoded.mint] = decoded
        this.mints[decoded.address] = decoded.mint

        if (emit) {
            this.emit(isExisting ? 'update' : 'add', decoded)
        }
    }

    public handleTransaction({ accounts, instructions }: WalletDatasourceTransaction) {
        const closedTokenAccounts = getCloseTokenAccountInstructions(accounts, instructions)

        for (const { address, owner } of closedTokenAccounts) {
            if (owner === this.wallet && isKeyOf(this.mints, address)) {
                const tokenAccount = this.accounts[this.mints[address]]

                delete this.accounts[this.mints[address]]
                delete this.mints[address]

                this.emit('remove', tokenAccount)
            }
        }
    }

    protected decodeTokenAccount(account: Account<Uint8Array>): TokenAccountType {
        return transform(decodeToken(account), (decoded) => ({ address: decoded.address, ...pick(decoded.data, 'owner', 'mint', 'amount') }))
    }
}
