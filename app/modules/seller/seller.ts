import type { Repository } from 'typeorm'
import type { SellConfig } from '../../config/sell'
import type { SellFailedTransaction } from '../../entities/sell-failed-transaction'
import type { AggregatedBondingCurve } from '../aggregator/types'
import type { GlobalAccount } from '../indexer/types/entities/global-account'
import type { TokenAccount } from '../wallet/types'
import type { Wallet } from '../wallet/wallet'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { timestamp } from '@kdt310722/utils/time'
import { calculateSolOut } from '@kdt-sol/pumpfun-sdk'
import PQueue from 'p-queue'
import { aggregator } from '../../common/aggregator'
import { getGlobalAccount } from '../../common/indexer'
import { sell } from '../../common/sell'
import { database } from '../../core/database'
import { getWallet } from '../../core/wallet'
import { SellOrder } from '../../entities/sell-order'
import { SellTransaction } from '../../entities/sell-transaction'
import { getPumpFunTokenPrice } from '../../utils/pumpfun'
import { computePercentageOf, computeStopLoss } from './utils'

export type SellerEvents = {
    error: (error: unknown) => void
    stopped: (reason: string) => void
    holdingTime: (date: Date) => void
    tpAndSl: (tp: bigint, sl: bigint, id?: number, idCount?: number) => void
    holding: (holding: bigint) => void
    sold: (transaction: SellTransaction) => void
    failed: (transaction: SellFailedTransaction) => void
}

export class Seller extends Emitter<SellerEvents, true> {
    public isSelling = false
    public isStopping = false
    public isStopped = false

    protected readonly repository: Repository<SellOrder>
    protected readonly config: SellConfig
    protected readonly wallet: Wallet
    protected readonly globalAccount: GlobalAccount
    protected readonly tokenAmount: bigint
    protected readonly saveQueue: PQueue
    protected readonly handleQueue: PQueue
    protected readonly tpStep: bigint
    protected readonly slStep: bigint
    protected readonly tpCounts = new Map<number, number>()

    protected hasTrade = false
    protected tokenBalance: bigint
    protected startedAt?: number

    protected timer?: NodeJS.Timeout
    protected bondingCurveHandler?: (mint: string, bondingCurve: AggregatedBondingCurve) => void
    protected tokenAccountHandler?: (tokenAccount: TokenAccount) => void

    public constructor(protected readonly order: SellOrder) {
        super()

        this.tokenAmount = order.buyTransaction.receivedTokens
        this.config = order.config
        this.repository = database.getRepository(SellOrder)
        this.saveQueue = new PQueue({ concurrency: 1, autoStart: false })
        this.handleQueue = new PQueue({ concurrency: 1, autoStart: false })
        this.wallet = getWallet()
        this.globalAccount = getGlobalAccount()
        this.tpStep = computePercentageOf(order.amount, order.config.tpIncrease)
        this.slStep = computePercentageOf(order.amount, order.config.slDecrease)
        this.tokenBalance = this.wallet.tokenAccount.get(order.mint).amount
    }

    public get isReady() {
        return !this.order.isCompleted && !this.isSelling && !this.isStopping && !this.isStopped
    }

    public async handleHolding(bondingCurve: AggregatedBondingCurve) {
        if (!this.isReady) {
            return
        }

        if (!this.hasTrade) {
            this.hasTrade = true
            this.holdUntil(this.config.holding.hasTrades)
        }

        const holding = tap(calculateSolOut(bondingCurve, this.tokenAmount, bondingCurve.feeBasisPoints ?? this.globalAccount.feeBasisPoints, bondingCurve.creatorFeeBasisPoints ?? this.globalAccount.creatorFeeBasisPoints), (holding) => this.emit('holding', holding))

        if ((isNullish(this.startedAt) || Date.now() - this.startedAt > this.config.applySlAfter) && holding <= this.order.sl) {
            return this.sell('stop loss')
        }

        const price = getPumpFunTokenPrice(bondingCurve)

        if (price >= this.config.maxPrice) {
            return this.sell('max price')
        }

        if (holding >= this.order.tp) {
            let count = 0

            while (this.order.tp < holding) {
                const tp = this.order.tp + this.tpStep
                const sl = computeStopLoss(tp - this.tpStep, this.config.slDecrease)

                this.updateTpAndSl(tp, sl)
                count++
            }

            if (count >= this.config.immediate.tpCount) {
                return this.sell('immediate')
            }
        }
    }

    public async start() {
        this.saveQueue.start()

        const bondingCurve = aggregator.get(this.order.mint)?.bondingCurve
        const tokenBalance = this.wallet.tokenAccount.get(this.order.mint)?.amount
        const holdUntil = this.order.holdUntil.getTime()

        if (tokenBalance === 0n) {
            return this.stop('balance is zero')
        }

        if (isNullish(bondingCurve) || isNullish(tokenBalance) || tokenBalance !== this.tokenAmount || Date.now() >= holdUntil) {
            return this.sell('init')
        }

        this.startedAt = Date.now()

        this.holdUntil(holdUntil - Date.now())
        this.emit('tpAndSl', this.order.tp, this.order.sl)
        this.handleQueue.start()
        this.registerListeners()
        this.handleBondingCurve(bondingCurve)
        this.handleTokenBalance(tokenBalance)
    }

    public async stop(reason: string) {
        this.tpCounts.clear()

        try {
            this.isStopping = true
            this.handleQueue.pause()
            this.updateOrder({ isCompleted: true })

            await this.saveQueue.onIdle().then(() => this.saveQueue.pause())

            if (this.timer) {
                clearTimeout(this.timer)
            }

            aggregator.removeListener('bondingCurve', this.bondingCurveHandler!)
            this.wallet.tokenAccount.removeListener('update', this.tokenAccountHandler!)

            this.isStopped = true
            this.emit('stopped', reason)
        } finally {
            this.isStopping = false
        }
    }

    protected updateTpAndSl(tp: bigint, sl: bigint, count?: number) {
        const id = timestamp()
        const idCount = (this.tpCounts.get(id) ?? 0) + 1

        let totalIdCount = idCount

        for (let i = 1; i <= this.config.immediate.duration; i++) {
            totalIdCount += this.tpCounts.get(id - i) ?? 0
        }

        this.tpCounts.set(id, idCount)

        if (isNullish(count)) {
            count = this.order.tpCount + 1
        }

        this.updateOrder({ tp, sl, tpCount: count })
        this.emit('tpAndSl', tp, sl, id, totalIdCount)

        if (totalIdCount >= this.config.immediate.tpCount) {
            return tap(void 0, () => this.sell('immediate'))
        }

        if (count > 0) {
            this.holdUntil(Math.max(this.config.holding.min, this.config.holding.maxPerTp - (this.config.holding.decrease * (count - 1))))
        }
    }

    protected holdUntil(ms: number) {
        if (this.timer) {
            clearTimeout(this.timer)
        }

        const holdUntil = new Date(Date.now() + ms)

        this.updateOrder({ holdUntil })
        this.timer = setTimeout(() => this.sell('timeout'), ms)
        this.emit('holdingTime', holdUntil)
    }

    protected async sell(reason: string) {
        if (!this.isReady) {
            return
        }

        this.isSelling = true

        try {
            const result = await sell(this.order.buyTransaction.tokenInfo, { ...this.config, order: this.order, reason })

            if (result instanceof SellTransaction) {
                this.updateOrder({ sellTransaction: result })
                this.emit('sold', result)
            } else {
                this.emit('failed', result)
            }
        } catch (error) {
            if (error instanceof Error && this.tokenBalance === 0n) {
                reason = `${reason} (error: ${error.message})`
            } else {
                this.emit('error', error)
            }
        } finally {
            this.isSelling = false
        }

        await this.stop(reason)
    }

    protected updateOrder(data: { [K in keyof SellOrder]?: SellOrder[K] }) {
        for (const [key, value] of Object.entries(data)) {
            if (notNullish(value)) {
                this.order[key] = value
            }
        }

        this.saveQueue.add(async () => this.repository.save(this.order)).catch((error) => this.emit('error', error))
    }

    protected handleBondingCurve(bondingCurve: AggregatedBondingCurve) {
        if (bondingCurve.slot > this.order.buyTransaction.slot) {
            Promise.resolve().then(() => this.handleQueue.add(() => this.handleHolding(bondingCurve)))
        }
    }

    protected handleTokenBalance(balance: bigint) {
        this.tokenBalance = balance

        if (this.isReady && balance !== this.tokenAmount) {
            Promise.resolve().then(() => this.stop('balance changed'))
        }
    }

    protected registerListeners() {
        aggregator.on('bondingCurve', this.bondingCurveHandler = (mint, bondingCurve) => mint === this.order.mint && this.handleBondingCurve(bondingCurve))
        this.wallet.tokenAccount.on('update', this.tokenAccountHandler = (tokenAccount) => tokenAccount.mint === this.order.mint && this.handleTokenBalance(tokenAccount.amount))
    }
}
