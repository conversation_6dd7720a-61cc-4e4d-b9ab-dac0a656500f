import type { BuyNotificationData } from './buy'
import { formatSol } from '../../../utils/formatters/format-sol'
import { TOKEN_HELP_LINKS } from '../constants'

export function getNftyBuyNotificationMessage({ transaction }: BuyNotificationData) {
    const title = 'Buy Alert'
    const link = TOKEN_HELP_LINKS['Chart'].replace('__MINT__', transaction.tokenInfo.mint)

    const message = `Token: ${transaction.tokenInfo.name} (${transaction.tokenInfo.symbol})
Amount: ${formatSol(transaction.amount)}`

    return { message, title, link }
}
