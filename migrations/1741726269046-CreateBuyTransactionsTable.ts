import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateBuyTransactionsTable1741726269046 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "buy_transactions" ("id" SERIAL NOT NULL, "slot" integer NOT NULL, "signature" character varying NOT NULL, "mint" character varying NOT NULL, "token_info" json NOT NULL, "amount" bigint NOT NULL, "slippage" integer NOT NULL, "priority_fee" bigint NOT NULL, "tip" bigint NOT NULL, "sol_cost" bigint NOT NULL, "received_tokens" bigint NOT NULL, "balance_before" bigint NOT NULL, "balance_after" bigint NOT NULL, "bonding_curve" json NOT NULL, "process_time" bigint NOT NULL, "send_time" bigint NOT NULL, "confirmation_time" integer NOT NULL, CONSTRAINT "PK_34b33c3aaedfdbfa4d809a0d618" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_e1030e76d0ad796c7fe1d4d673" ON "buy_transactions" ("slot") `)
        await queryRunner.query(`CREATE INDEX "IDX_21cfbb1f0313ecc085906f8f01" ON "buy_transactions" ("signature") `)
        await queryRunner.query(`CREATE INDEX "IDX_28a1aa7595c3caa00cb61c2ea8" ON "buy_transactions" ("mint") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_28a1aa7595c3caa00cb61c2ea8"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_21cfbb1f0313ecc085906f8f01"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_e1030e76d0ad796c7fe1d4d673"`)
        await queryRunner.query(`DROP TABLE "buy_transactions"`)
    }
}
