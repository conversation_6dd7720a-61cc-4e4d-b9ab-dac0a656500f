import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { filterByValue, LruMap } from '@kdt310722/utils/object'
import { createDeferred, type DeferredPromise } from '@kdt310722/utils/promise'
import { CommitmentLevel, type GeyserClient, type SubscribeUpdate } from '@kdt-sol/geyser-client'
import PQueue from 'p-queue'

export interface Slot {
    slot: number
    status: CommitmentLevel
    blockHeight: number
    blockhash: string
    blockTime: number
}

export interface PendingSlot extends Omit<Partial<Slot>, 'slot'> {
    slot: number
}

export type GeyserSlotEvents = {
    slot: (slot: Slot) => void
    confirmedSlot: (slot: Slot) => void
    latency: (latency: number) => void
}

export interface GeyserSlotConfig {
    maxSlotsInCache: number
    maxPendingSlots: number
}

export class GeyserSlot extends Emitter<GeyserSlotEvents, true> {
    protected readonly queue: PQueue
    protected readonly slots: LruMap<number, Slot>
    protected readonly pendingSlots: LruMap<number, PendingSlot>

    protected slotSubscriptionId?: string
    protected blockMetaSubscriptionId?: string
    protected hasConfirmedSlot?: DeferredPromise<void>

    #latestSlot?: Slot
    #latestConfirmedSlot?: Slot
    #latency?: number

    public constructor(protected readonly client: GeyserClient, { maxSlotsInCache, maxPendingSlots }: GeyserSlotConfig) {
        super()

        this.queue = new PQueue({ concurrency: 1 })
        this.slots = new LruMap(maxSlotsInCache)
        this.pendingSlots = new LruMap(maxPendingSlots)

        this.client.on('data', this.handleData.bind(this))
    }

    public get latestSlot() {
        return this.#latestSlot!
    }

    public get latestConfirmedSlot() {
        return this.#latestConfirmedSlot!
    }

    public get latency() {
        return this.#latency
    }

    public async subscribe() {
        this.hasConfirmedSlot = createDeferred()
        this.slotSubscriptionId = await this.client.subscribe('slots', {})
        this.blockMetaSubscriptionId = await this.client.subscribe('blocksMeta', {})

        return this.hasConfirmedSlot
    }

    protected handleData(subscriptionId: string, data: SubscribeUpdate) {
        if (subscriptionId === this.slotSubscriptionId && notNullish(data.slot)) {
            return this.updateSlot({ slot: Number(data.slot.slot), status: data.slot.status })
        }

        if (subscriptionId === this.blockMetaSubscriptionId && notNullish(data.blockMeta)) {
            const blockHeight = data.blockMeta.blockHeight ? Number(data.blockMeta.blockHeight.blockHeight) : undefined
            const blockTime = data.blockMeta.blockTime ? Number(data.blockMeta.blockTime.timestamp) : undefined

            this.updateSlot({ slot: Number(data.blockMeta.slot), blockHeight, blockhash: data.blockMeta.blockhash, blockTime })

            if (notNullish(data.createdAt)) {
                this.updateLatency(Date.now() - data.createdAt.getTime())
            }
        }
    }

    protected updateLatency(latency: number) {
        this.emit('latency', this.#latency = latency)
    }

    protected updateSlot(slot: PendingSlot) {
        const update = () => {
            const newSlot: PendingSlot = { slot: slot.slot, ...this.pendingSlots.get(slot.slot), ...this.slots.get(slot.slot), ...filterByValue(slot, notNullish) }

            if (this.isFullFilledSlot(newSlot)) {
                this.slots.set(slot.slot, newSlot)
                this.pendingSlots.delete(slot.slot)
                this.setLatestSlot(newSlot)
            } else {
                this.pendingSlots.set(slot.slot, newSlot)
            }
        }

        Promise.resolve().then(() => this.queue.add(update))
    }

    protected setLatestSlot(slot: Slot) {
        if (isNullish(this.#latestSlot) || slot.slot >= this.#latestSlot.slot) {
            this.emit('slot', this.#latestSlot = slot)

            if (this.#latestSlot.status === CommitmentLevel.CONFIRMED) {
                this.emit('confirmedSlot', this.#latestConfirmedSlot = this.#latestSlot)

                if (this.hasConfirmedSlot && !this.hasConfirmedSlot.isSettled) {
                    this.hasConfirmedSlot.resolve()
                }
            }
        }
    }

    protected isFullFilledSlot(slot: PendingSlot): slot is Slot {
        return !['slot', 'status', 'blockHeight', 'blockhash', 'blockTime'].some((key) => isNullish(slot[key]))
    }
}
