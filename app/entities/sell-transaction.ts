import type { SellConfirmResultSuccess, SellParams } from '../common/sell'
import type { AggregatedBondingCurve } from '../modules/aggregator/types'
import type { Token } from '../modules/indexer/types/entities/token'
import { pick } from '@kdt310722/utils/object'
import { type Address, type Signature, signature } from '@solana/kit'
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { getWallet } from '../core/wallet'
import { BigIntColumn } from '../utils/database/columns/bigint-column'
import { JsonColumn } from '../utils/database/columns/json-column'
import { getBalanceChange } from '../utils/transactions'
import { BuyTransaction } from './buy-transaction'

@Entity()
export class SellTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column('varchar')
    public declare mint: Address

    @JsonColumn()
    public declare tokenInfo: Token

    @BigIntColumn()
    public declare amount: bigint

    @JsonColumn()
    public declare config: SellParams

    @Column()
    public declare sender: string

    @Column()
    public declare slot: number

    @Column('varchar')
    public declare signature: Signature

    @BigIntColumn()
    public declare tokensCost: bigint

    @BigIntColumn()
    public declare receivedSol: bigint

    @BigIntColumn()
    public declare balanceBefore: bigint

    @BigIntColumn()
    public declare balanceAfter: bigint

    @JsonColumn()
    public declare bondingCurve: Omit<AggregatedBondingCurve, 'sortKey' | 'slot'>

    @BigIntColumn()
    public declare processTime: bigint

    @BigIntColumn()
    public declare sendTime: bigint

    @Column()
    public declare confirmationTime: number

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date

    public static fromSellConfirmResult(token: Token, amount: bigint, result: SellConfirmResultSuccess, config: SellParams) {
        const entity = new SellTransaction()
        const trade = BuyTransaction.getTradeEvent(result)
        const wallet = getWallet().signer.address

        if (trade.isBuy) {
            throw new Error('Expected a sell trade event')
        }

        const { pre: preBalance, post: postBalance } = getBalanceChange(result, wallet)

        entity.mint = result.mint
        entity.tokenInfo = token
        entity.amount = amount
        entity.config = config
        entity.sender = result.sender.name
        entity.slot = result.slot
        entity.signature = signature(result.signature)
        entity.tokensCost = trade.tokenAmount
        entity.receivedSol = trade.solAmount
        entity.bondingCurve = pick(trade, 'virtualSolReserves', 'virtualTokenReserves', 'realSolReserves', 'realTokenReserves', 'creator', 'feeBasisPoints', 'creatorFeeBasisPoints')
        entity.balanceBefore = preBalance
        entity.balanceAfter = postBalance
        entity.processTime = result.processTime
        entity.sendTime = result.sendTime
        entity.confirmationTime = result.took

        return entity
    }
}
