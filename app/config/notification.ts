import { z } from 'zod'
import { nullish } from '../utils/schemas/nullish'

const schema = z.object({
    name: nullish(z.string()),
    botToken: nullish(z.string()),
    transaction: nullish(z.string()),
    trade: nullish(z.string()),
    buy: nullish(z.string()),
    sell: nullish(z.string()),
    ntfy: nullish(z.string()),
})

export const notification = z.union([schema, z.boolean().transform((val) => (val ? schema.parse({}) : {}))]).default({})
