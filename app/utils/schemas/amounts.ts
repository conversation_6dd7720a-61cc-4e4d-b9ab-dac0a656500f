import { z } from 'zod'
import { PUMPFUN_TOKEN_DENOMINATOR, SOL_DENOMINATOR } from '../../constants'

function toBigInt(val: number) {
    return BigInt(val.toString().split('.')[0])
}

export const solAmount = z.coerce.number().safe().finite().transform((val) => toBigInt(val * SOL_DENOMINATOR))

export const pumpfunTokenAmount = z.coerce.number().safe().finite().transform((val) => toBigInt(val * PUMPFUN_TOKEN_DENOMINATOR))
