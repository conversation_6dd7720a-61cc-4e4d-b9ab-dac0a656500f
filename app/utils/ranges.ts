import { isNullish, notNullish } from '@kdt310722/utils/common'

export interface Range<T extends number | bigint> {
    gt?: T
    gte?: T
    lt?: T
    lte?: T
}

export function isValidRange<T extends number | bigint>({ gt, gte, lt, lte }: Range<T>) {
    const gtOrGte = gt ?? gte
    const ltOrLte = lt ?? lte

    if (isNullish(gtOrGte) && isNullish(ltOrLte)) {
        return false
    }

    if (notNullish(gtOrGte)) {
        return isNullish(ltOrLte) || gtOrGte <= ltOrLte
    }

    return true
}

export function isInRange<T extends number | bigint>(value: T, { gt, gte, lt, lte }: Range<T>) {
    if (notNullish(gt) && value <= gt) {
        return false
    }

    if (notNullish(gte) && value < gte) {
        return false
    }

    if (notNullish(lt) && value >= lt) {
        return false
    }

    return isNullish(lte) || value <= lte
}
