import type { AggregatedTokenStats } from '../modules/aggregator/types'
import { z } from 'zod'
import { nullish } from '../utils/schemas/nullish'
import { bigIntRange, numberRange } from '../utils/schemas/ranges'

export type FilterKey = Exclude<keyof AggregatedTokenStats, 'sortKey' | 'signature' | 'slot' | 'transactionIndex' | 'eventIndex'>

export const filterSchema = z.object({
    price: nullish(bigIntRange),
    atl: nullish(bigIntRange),
    upFromAtl: nullish(numberRange),
    ath: nullish(bigIntRange),
    downFromAth: nullish(numberRange),
    buyVolume: nullish(bigIntRange),
    sellVolume: nullish(bigIntRange),
    volume: nullish(bigIntRange),
    buysCount: nullish(numberRange),
    sellsCount: nullish(numberRange),
    tradesCount: nullish(numberRange),
    lastTrade: nullish(numberRange),
})

const schema = z.object({
    recheckInterval: z.number().int().positive().default(60 * 1000),
    maxAge: z.number().int().positive().default(30 * 1000),
})

export const filter = schema.default({})
