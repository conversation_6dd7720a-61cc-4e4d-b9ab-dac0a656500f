{"type": "module", "private": true, "packageManager": "pnpm@10.12.1", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "UNLICENSED", "engines": {"node": ">=20.12.0"}, "scripts": {"build": "rm -rf dist && tsc --noEmit && tsup", "run:build": "NODE_ENV=build node dist/index.js", "dev": "node bin/run.js", "start": "NODE_ENV=production node bin/run.js", "typeorm": "node bin/typeorm.js -d app/core/database.ts", "migration:generate": "node bin/typeorm.js -d app/core/database.ts migration:generate", "migration:create": "node bin/typeorm.js migration:create", "up": "ncu -i -x express", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks"}, "dependencies": {"@kdt-sol/geyser-client": "^0.0.6", "@kdt-sol/pumpfun-sdk": "^0.3.0", "@kdt310722/config": "^0.0.4", "@kdt310722/logger": "^0.0.12", "@kdt310722/rpc": "^0.2.1", "@kdt310722/utils": "^0.0.19", "@solana-program/compute-budget": "^0.8.0", "@solana-program/memo": "^0.7.0", "@solana-program/system": "^0.7.0", "@solana-program/token": "^0.5.1", "@solana/kit": "^2.1.1", "big.js": "^7.0.1", "bottleneck": "^2.19.5", "bs58": "^6.0.0", "change-case": "^5.4.4", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^4.21.2", "fast-glob": "^3.3.3", "helmet": "^8.1.0", "p-queue": "^8.1.0", "pg": "^8.16.0", "pluralize": "^8.0.0", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.24", "typeorm-naming-strategies": "^4.1.0", "undici": "^7.10.0", "ws": "^8.18.2", "zod": "^3.25.57", "zod-validation-error": "^3.4.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt-farm/eslint-config": "^0.0.2", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.11.31", "@types/big.js": "^6.2.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.0", "@types/pluralize": "^0.0.33", "@types/ws": "^8.18.1", "eslint": "^9.28.0", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "simple-git-hooks": "^2.13.0", "ts-node-maintained": "^10.9.5", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "pnpm": {"ignoredBuiltDependencies": ["@kdt-sol/geyser-client", "@kdt-sol/pumpfun-sdk", "@kdt310722/config", "@kdt310722/eslint-config", "@kdt310722/logger", "@kdt310722/rpc", "@kdt310722/utils"], "onlyBuiltDependencies": ["@kdt-farm/eslint-config", "@swc/core", "esbuild", "protobufjs", "simple-git-hooks", "unrs-resolver"]}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx tsc --noEmit && npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}