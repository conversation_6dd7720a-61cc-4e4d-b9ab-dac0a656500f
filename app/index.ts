import { tap } from '@kdt310722/utils/function'
import { waterfall } from '@kdt310722/utils/promise'
import { initializeAggregator } from './common/aggregator'
import { initializeBuyer } from './common/buyer'
import { initializeFilter } from './common/filter'
import { initializeIndexerClient } from './common/indexer'
import { initializeMonitor } from './common/monitor'
import { initializeSeller } from './common/seller'
import { initializeTracker } from './common/tracker'
import { initializeBlock } from './core/block'
import { initializeDatabase } from './core/database'
import { initializeGeyserClient } from './core/geyser'
import { logger } from './core/logger'
import { initializeRpcClient } from './core/rpc-client'
import { initializeRpcServer, startRpcServer } from './core/rpc-server'
import { initializeRpcWebsocketClient } from './core/rpc-websocket-client'
import { initializeTransactionSenders } from './core/transaction'
import { initializeWallet } from './core/wallet'
import 'reflect-metadata'
import './common/cleaner'

const initTimer = tap(logger.createTimer(), () => logger.info('Initializing application...'))
const init = waterfall([initializeFilter, initializeDatabase, initializeTransactionSenders, initializeRpcServer, initializeRpcClient, initializeRpcWebsocketClient, initializeGeyserClient, initializeBlock, initializeMonitor, initializeWallet, initializeBuyer, initializeIndexerClient, initializeAggregator, initializeSeller, initializeTracker] as any)

const app = init.then(() => logger.stopTimer(initTimer, 'info', 'Application initialized!')).then(() => true).catch((error) => {
    logger.forceExit(1, 'fatal', 'Failed to initialize application', error)
})

app.then((success) => success && startRpcServer()).catch((error) => {
    logger.forceExit(1, 'fatal', error)
})
