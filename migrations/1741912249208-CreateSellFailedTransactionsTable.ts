import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateSellFailedTransactionsTable1741912249208 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "sell_failed_transactions" ("id" SERIAL NOT NULL, "mint" character varying NOT NULL, "token_info" json NOT NULL, "amount" bigint NOT NULL, "config" json NOT NULL, "sender" character varying NOT NULL, "slot" integer NOT NULL, "signature" character varying NOT NULL, "err" json NOT NULL, "process_time" bigint NOT NULL, "send_time" bigint NOT NULL, "confirmation_time" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_ff9060a7a4ff155331b7241f268" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_c59b9a2c192fffd5829f9b2ed6" ON "sell_failed_transactions" ("mint") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sell_orders" DROP COLUMN "amount"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_c59b9a2c192fffd5829f9b2ed6"`)
    }
}
