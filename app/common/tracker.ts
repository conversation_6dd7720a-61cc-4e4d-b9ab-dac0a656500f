import type { DexScreenerPaymentTransaction } from '../types/tracker'
import { highlight, LogLevel } from '@kdt310722/logger'
import { RpcWebSocketClient } from '@kdt310722/rpc'
import { LruSet } from '@kdt310722/utils/array'
import { join } from '@kdt310722/utils/buffer'
import { isEmpty } from '@kdt310722/utils/common'
import { tap, transform } from '@kdt310722/utils/function'
import { shorten } from '@kdt310722/utils/string'
import { MoreThanOrEqual, type Repository } from 'typeorm'
import { config } from '../config'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { getWallet } from '../core/wallet'
import { PaymentTransaction } from '../entities/payment-transaction'
import { toPaymentTransactionEntity } from '../utils/formatters/to-payment-transaction-entity'
import { aggregator } from './aggregator'
import { availableToBuyTokens, buy } from './buyer'
import { notification } from './notification'

const logger = createChildLogger('common:tracker')
const tracker = new RpcWebSocketClient(config.tracker.url, config.tracker)
const trackedTokens = new LruSet<string>(5000)

let repository: Repository<PaymentTransaction> | undefined

function getRepository() {
    return repository ??= database.getRepository(PaymentTransaction)
}

function handlePaymentNotification(data: DexScreenerPaymentTransaction) {
    const start = process.hrtime.bigint()
    const tokens = aggregator.getTokensByAddress(data.payer)

    const buyTokens = tokens.filter(({ token }) => availableToBuyTokens.has(token.mint) && !trackedTokens.has(token.mint)).toSorted((a, b) => b.token.createdAt.getTime() - a.token.createdAt.getTime())
    const processTime = process.hrtime.bigint() - start

    if (buyTokens.length > 0) {
        const printIgnore = (reason: string) => logger.info(`Ignore transaction ${highlight(shorten(data.signature, 4))}: ${highlight(reason)}`)

        if (!config.buy.enabled) {
            printIgnore('Buy is disabled')
        } else if (getWallet().balance - config.buy.amount < config.buy.minimumWalletBalance) {
            printIgnore('Insufficient balance')
        } else {
            buy(buyTokens[0].token.mint, { paymentTransaction: data }).catch((error) => logger.error(error))
        }
    } else {
        logger.info(`No tokens to buy for transaction ${highlight(shorten(data.signature, 4))}`)
    }

    notification.sendPaymentTransactionNotification({ transaction: data, processTime, tokens, availableToBuyTokens: buyTokens })

    for (const { token } of tokens) {
        trackedTokens.add(token.mint)
    }

    getRepository().save(toPaymentTransactionEntity(data, tokens.map(({ token }) => token))).catch((error) => {
        logger.error('Failed to save payment transaction to database', error, { transaction: data })
    })
}

let isInitialized = false

tracker.socket.on('reconnect', (attempts) => logger.info(`Reconnecting to DexScreener Payment Tracker server (attempts: ${highlight(attempts)})...`))
tracker.socket.on('error', (error) => logger.error('Error occurred in DexScreener Payment Tracker server', error))
tracker.on('error', (error) => logger.error('Error occurred in DexScreener Payment Tracker server', error))
tracker.on('unhandledMessage', (message) => logger.warn('Received unhandled message from DexScreener Payment Tracker server', message))
tracker.on('unhandledRpcMessage', (message) => logger.warn('Received unhandled message from DexScreener Payment Tracker server', message))

tracker.on('notification', (method, params) => {
    if (method === 'payment') {
        handlePaymentNotification(params)
    }
})

tracker.socket.on('connected', () => {
    tracker.socket.resetRetryCount()

    if (isInitialized) {
        logger.info('Connected to DexScreener Payment Tracker server!')
    }
})

tracker.socket.on('disconnected', (code, reason, isExplicitlyClosed) => {
    const message = `Disconnected from DexScreener Payment Tracker server: ${highlight(`${code} - ${transform(join(reason), (r) => (isEmpty(r) ? 'EMPTY_REASON' : r))}`)}`

    if (tracker.socket.isReconnectAttemptReached) {
        logger.exit(1, 'fatal', message)
    } else if (isInitialized) {
        logger.log(isExplicitlyClosed ? LogLevel.INFO : LogLevel.WARN, message)
    }
})

async function loadTrackedTokens() {
    const transactions = await getRepository().find({ select: ['tokens'], where: { createdAt: MoreThanOrEqual(new Date(Date.now() - config.aggregator.maxTokenAge)) } })

    for (const { tokens } of transactions) {
        for (const token of tokens) {
            trackedTokens.add(token.mint)
        }
    }
}

export async function initializeTracker() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing tracker module...'))

    await loadTrackedTokens().then(() => tracker.socket.connect()).then(() => isInitialized = true).then(() => {
        logger.stopTimer(timer, 'info', 'Tracker module is initialized!')
    })
}
