import type { BondingCurveAccount } from '../modules/indexer/types/entities/bonding-curve'
import type { GlobalAccount } from '../modules/indexer/types/entities/global-account'
import { highlight, LogLevel } from '@kdt310722/logger'
import { join } from '@kdt310722/utils/buffer'
import { isEmpty, isNullish, notNullish } from '@kdt310722/utils/common'
import { transform } from '@kdt310722/utils/function'
import { pick } from '@kdt310722/utils/object'
import { createDeferred } from '@kdt310722/utils/promise'
import { fetchGlobal, GLOBAL_ACCOUNT_ADDRESS } from '@kdt-sol/pumpfun-sdk'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { rpcClient } from '../core/rpc-client'
import { rpcServer } from '../core/rpc-server'
import { IndexerClient } from '../modules/indexer/indexer-client'
import { showResubscribeEvents } from '../utils/cli'
import { getPumpFunMarketCap, getPumpFunTokenPrice } from '../utils/pumpfun'

rpcServer.ws.addEvent('indexer:slot')

const logger = createChildLogger('common:indexer')
const client = new IndexerClient(config.indexer.url, config.indexer)
const gapHandlers = new Set<(startSlot: number, endSlot: number) => void>()

let isInitialized = false
let isReady = false
let globalAccount: GlobalAccount | undefined
let latestSlot: number | undefined
let lastKnownSlotBeforeDisconnect: number | undefined
let firstReceivedSlot = createDeferred<number>()

export function onGap(handler: (startSlot: number, endSlot: number) => void) {
    gapHandlers.add(handler)
}

export function isIndexerReady() {
    return isReady
}

function handleGap(startSlot: number, endSlot: number) {
    logger.warn(`Slot gap found: ${highlight(startSlot)} - ${highlight(endSlot)}`)

    for (const handler of gapHandlers) {
        handler(startSlot, endSlot)
    }
}

client.notifications.on('slot', (slot) => {
    if (isNullish(latestSlot) || slot > latestSlot) {
        rpcServer.ws.emit('indexer:slot', latestSlot = slot)

        if (!firstReceivedSlot.isSettled) {
            firstReceivedSlot.resolve(slot)
        }
    }
})

client.notifications.on('globalAccount', (account) => {
    globalAccount = account
})

client.socket.on('connected', () => {
    if (isInitialized) {
        logger.info('Connected to Indexer server!')

        const handleSuccess = async (events: string[]) => {
            logger.info(`Resubscribed to ${highlight(events.length)} events on Indexer server!`)
            client.socket.resetRetryCount()

            if (notNullish(lastKnownSlotBeforeDisconnect)) {
                handleGap(lastKnownSlotBeforeDisconnect + 1, await firstReceivedSlot.then((slot) => slot - 1))
            }

            isReady = true
        }

        client.resubscribe((events) => logger.info(`Resubscribing to ${highlight(events.length)} events on Indexer server...${showResubscribeEvents() ? `\n  + ${events.map((i) => highlight(i)).join('\n  + ')}` : ''}`)).then(handleSuccess).catch(async (error) => {
            await Promise.resolve(logger.error('Failed to resubscribe to events on Indexer server', error)).then(() => client.socket.disconnect(false))
        })
    } else {
        isReady = true
    }
})

client.socket.on('disconnected', (code, reason, isExplicitly) => {
    isReady = false
    lastKnownSlotBeforeDisconnect = latestSlot
    firstReceivedSlot = createDeferred<number>()

    if (!client.socket.isReconnecting) {
        logger.log(isExplicitly ? LogLevel.INFO : LogLevel.WARN, `Disconnected from Indexer server: ${highlight(`${code} - ${transform(join(reason), (r) => (isEmpty(r) ? 'EMPTY_REASON' : r))}`)}`)
    }

    if (client.socket.isReconnectAttemptReached) {
        logger.exit(1, 'fatal', 'Failed to reconnect to Indexer server')
    }
})

function handleError(error: unknown) {
    Promise.resolve(logger.error('Indexer server error', error)).then(() => client.socket.disconnect(false))
}

function handleUnhandledMessage(message: unknown) {
    logger.warn('Received unhandled message from Indexer server', { message: Buffer.isBuffer(message) ? join(message) : message })
}

client.socket.on('reconnect', (attempts) => logger.info(`Reconnecting to Indexer server (attempts: ${highlight(attempts)})...`))
client.socket.on('error', handleError)

client.on('error', handleError)
client.on('unhandledMessage', handleUnhandledMessage)
client.on('unhandledRpcMessage', handleUnhandledMessage)

export const indexer = client

export async function fetchGlobalAccount() {
    return fetchGlobal(rpcClient, GLOBAL_ACCOUNT_ADDRESS, { commitment: 'confirmed' }).then((r): GlobalAccount => pick(r.data, 'feeRecipient', 'initialVirtualTokenReserves', 'initialVirtualSolReserves', 'initialRealTokenReserves', 'feeBasisPoints', 'tokenTotalSupply', 'creatorFeeBasisPoints'))
}

export function getGlobalAccount() {
    return globalAccount!
}

let solPrice: number | undefined

client.notifications.on('solPrice', (price) => {
    solPrice = price
})

export function getSolPrice() {
    return solPrice!
}

export async function fetchSolPrice() {
    return client.getSolPrice().then((price) => solPrice = price)
}

export function getMarketCap(bondingCurve: Pick<BondingCurveAccount, 'virtualSolReserves' | 'virtualTokenReserves'>) {
    return getPumpFunMarketCap(getPumpFunTokenPrice(bondingCurve), getGlobalAccount().tokenTotalSupply, getSolPrice())
}

export async function initializeIndexerClient() {
    const stop = logger.createLoading().start('Connecting to Indexer server...')

    await client.socket.connect().then(() => client.subscribe(['slot', 'globalAccount', 'solPrice'])).then(async () => globalAccount = await fetchGlobalAccount()).then(() => fetchSolPrice()).then(() => (isInitialized = true)).then(() => {
        stop('Connected to Indexer server!')
    })
}
