import { highlight } from '@kdt310722/logger'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { createDeferred } from '@kdt310722/utils/promise'
import { formatDate, toTimestamp } from '@kdt310722/utils/time'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { Aggregator } from '../modules/aggregator/aggregator'
import { toArray } from '../modules/indexer/methods/get-tokens'
import { indexer, onGap } from './indexer'

export const logger = createChildLogger('common:aggregator')
export const aggregator = new Aggregator()

export async function fetchGapTrades(startSortKey: bigint, endSortKey: bigint) {
    const timer = tap(logger.createTimer(), () => logger.info(`Fetching gap trades from ${highlight(startSortKey)} to ${highlight(endSortKey)}...`))

    return indexer.getTrades({ startSortKey, endSortKey }).then((trades) => tap(trades, () => logger.stopTimer(timer, 'info', `Found ${highlight(format(trades.length))} trades!`))).catch((error) => {
        return logger.forceExit(1, 'fatal', 'Failed to fetch gap trades', error)
    })
}

export async function fetchGapTokens(startSlot: number, endSlot: number) {
    const timer = tap(logger.createTimer(), () => logger.info(`Fetching gap tokens from ${highlight(startSlot)} to ${highlight(endSlot)}...`))

    return toArray(indexer.getTokens({ startSlot, endSlot })).then((tokens) => tap(tokens, () => logger.stopTimer(timer, 'info', `Found ${highlight(format(tokens.length))} tokens!`))).catch((error) => {
        return logger.forceExit(1, 'fatal', 'Failed to fetch gap tokens', error)
    })
}

onGap((startSlot, endSlot) => {
    fetchGapTokens(startSlot - 1, endSlot + 1).then((tokens) => {
        for (const token of tokens) {
            aggregator.handleToken(token)
        }
    })
})

function handleTradeGap(startSortKey: bigint, endSortKey: bigint) {
    logger.warn(`Trade gap found: ${highlight(startSortKey)} - ${highlight(endSortKey)}`)

    fetchGapTrades(startSortKey, endSortKey).then((trades) => {
        for (const trade of trades) {
            aggregator.handleGapTrade(trade)
        }

        aggregator.resume()
    })
}

let latestSortKey: bigint | undefined
let lastKnownSortKeyBeforeDisconnect: bigint | undefined
let firstReceivedSortKey = createDeferred<bigint>()

indexer.notifications.on('token', (token) => aggregator.handleToken(token))
indexer.notifications.on('gapToken', (token) => aggregator.handleToken(token))
indexer.notifications.on('gapTrade', (trade) => aggregator.handleGapTrade(trade))

const firstReceivedSlot = createDeferred<number>()

indexer.notifications.on('trade', (trade) => {
    aggregator.handleTrade(trade)

    if (isNullish(latestSortKey) || trade.sortKey > latestSortKey) {
        latestSortKey = trade.sortKey
    }

    if (!firstReceivedSortKey.isSettled) {
        firstReceivedSortKey.resolve(trade.sortKey)
    }

    if (!firstReceivedSlot.isSettled) {
        firstReceivedSlot.resolve(trade.slot)
    }
})

indexer.socket.on('connected', () => {
    if (notNullish(lastKnownSortKeyBeforeDisconnect)) {
        firstReceivedSortKey.then((firstSortKey) => handleTradeGap(lastKnownSortKeyBeforeDisconnect! + 1n, firstSortKey - 1n))
    }
})

indexer.socket.on('disconnected', () => {
    aggregator.pause()
    lastKnownSortKeyBeforeDisconnect = latestSortKey
    firstReceivedSortKey = createDeferred<bigint>()
})

async function loadTokenStats(mints: string[]) {
    const timer = logger.createTimer()
    const endSortKey = (await firstReceivedSortKey) - 1n
    const stats = await indexer.getStats(mints, { endSortKey })

    for (const [index, tokenStats] of stats.entries()) {
        if (notNullish(tokenStats)) {
            aggregator.handleTokenStats(mints[index], tokenStats)
        }
    }

    logger.stopTimer(timer, 'debug', 'Token stats filled!')
}

async function loadTraders(mints: string[]) {
    const timer = logger.createTimer()
    const traders = await indexer.getTraders(mints)

    for (const [mint, trader] of Object.entries(traders)) {
        aggregator.handleTraders(mint, trader)
    }

    logger.stopTimer(timer, 'debug', 'Traders filled!')
}

async function loadBondingCurves(mints: string[]) {
    const timer = logger.createTimer()
    const bondingCurves = await indexer.getBondingCurves(mints)

    for (const [index, bondingCurve] of bondingCurves.entries()) {
        if (notNullish(bondingCurve)) {
            aggregator.handleBondingCurve(mints[index], bondingCurve)
        }
    }

    logger.stopTimer(timer, 'debug', 'Bonding curves filled!')
}

async function loadTokens() {
    const startDate = new Date(Date.now() - config.aggregator.maxTokenAge)
    const startTime = toTimestamp(startDate)
    const endSlot = await firstReceivedSlot

    const tokenTimer = tap(logger.createTimer(), () => logger.info(`Fetching tokens start from ${highlight(formatDate(startDate))} to slot ${highlight(endSlot)}...`))
    const total = tap(await indexer.getTokensCount({ startTime, endSlot }), (count) => logger.stopTimer(tokenTimer, 'info', `Total tokens to fill: ${highlight(format(count))}`))

    if (total === 0) {
        return
    }

    let filled = 0

    for await (const tokens of indexer.getTokens({ startTime, endSlot, limit: config.aggregator.chunkSize })) {
        const timer = logger.createTimer()
        const mints: string[] = []

        for (const token of tokens) {
            mints.push(token.mint)
            aggregator.handleToken(token)
        }

        await loadBondingCurves(mints)
        await loadTraders(mints)
        await loadTokenStats(mints)

        logger.stopTimer(timer, 'info', `Filled ${highlight(format(filled += tokens.length))} / ${highlight(format(total))} tokens!`)
    }
}

let isInitialized_ = false

export function isInitialized() {
    return isInitialized_
}

export async function initializeAggregator() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing aggregator...'))

    await indexer.subscribe(['trade', 'gapTrade', 'token', 'gapToken']).then(() => loadTokens()).then(() => aggregator.start()).then(() => isInitialized_ = true).then(() => {
        logger.stopTimer(timer, 'info', 'Aggregator is initialized!')
    })
}
