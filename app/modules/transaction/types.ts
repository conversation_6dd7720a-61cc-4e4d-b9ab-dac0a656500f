import type { TransactionMessage as BaseTransactionMessage, IAccountMeta, ITransactionMessageWithFeePayer, TransactionMessageWithBlockhashLifetime } from '@solana/kit'

export type BlockhashLifetime = TransactionMessageWithBlockhashLifetime['lifetimeConstraint']

export type TransactionMessage = ITransactionMessageWithFeePayer & Extract<BaseTransactionMessage, { version: 'legacy' }> & TransactionMessageWithBlockhashLifetime

export interface PriorityFee {
    units: number
    lamports: bigint
    accounts?: IAccountMeta[]
}

export interface SendStats {
    buildTime: bigint
    sendTime: bigint
    parseResponseTime: bigint
}
