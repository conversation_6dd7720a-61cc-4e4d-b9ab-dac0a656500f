import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddSellTransactionColumnToSellOrdersTable1741915983765 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sell_orders" ADD "sell_transaction_id" integer`)
        await queryRunner.query(`ALTER TABLE "sell_orders" ADD CONSTRAINT "UQ_066a3c3275fa3c0d589da6826b2" UNIQUE ("sell_transaction_id")`)
        await queryRunner.query(`ALTER TABLE "sell_orders" ADD CONSTRAINT "FK_066a3c3275fa3c0d589da6826b2" FOREIGN KEY ("sell_transaction_id") REFERENCES "sell_transactions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sell_orders" DROP CONSTRAINT "FK_066a3c3275fa3c0d589da6826b2"`)
        await queryRunner.query(`ALTER TABLE "sell_orders" DROP CONSTRAINT "UQ_066a3c3275fa3c0d589da6826b2"`)
        await queryRunner.query(`ALTER TABLE "sell_orders" DROP COLUMN "sell_transaction_id"`)
    }
}
