import { z } from 'zod'
import { swap } from './buy'

const seconds = z.number().int().nonnegative().transform((val) => val * 1000)

export const retry = z.object({
    enabled: z.boolean().default(true),
    attempts: z.number().int().positive().default(3),
    delay: z.number().int().nonnegative().default(0),
})

export const holding = z.object({
    withoutTrades: seconds,
    hasTrades: seconds,
    maxPerTp: seconds,
    min: seconds,
    decrease: seconds,
})

export const immediate = z.object({
    tpCount: z.number().int().positive(),
    duration: z.number().int().nonnegative(),
})

export const schema = swap.extend({
    holding,
    immediate,
    retry: retry.default({}),
    enabled: z.boolean().default(true),
    maxPrice: z.coerce.bigint().min(0n).default(375_000n),
    tp: z.number().positive(),
    tpIncrease: z.number().positive(),
    sl: z.number().positive(),
    slDecrease: z.number().positive(),
    applySlAfter: seconds,
})

export const sell = schema.nullish()

export type SellConfig = Omit<z.infer<typeof schema>, 'enabled'>
