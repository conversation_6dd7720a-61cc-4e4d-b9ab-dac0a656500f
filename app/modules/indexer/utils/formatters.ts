import type { Token, TokenApiResponse } from '../types/entities/token'
import type { TokenStats, TokenStatsApiResponse } from '../types/entities/token-stats'
import type { Trade, TradeApiResponse } from '../types/entities/trade'

export const formatToken = (response: TokenApiResponse): Token => ({ ...response, createdAt: new Date(response.createdAt) })

export const formatTrade = (response: TradeApiResponse): Trade => ({ ...response, timestamp: new Date(response.timestamp) })

export const formatTokenStats = (response: TokenStatsApiResponse): TokenStats => ({ ...response, lastTrade: new Date(response.lastTrade) })
