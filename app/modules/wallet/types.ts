import type { Emitter } from '@kdt310722/utils/event'
import type { Account, Address, IAccountMeta, Lamports, Signature, TokenBalance } from '@solana/kit'
import type { RawInstruction } from '../../utils/rpc-client/instructions'

export interface TokenAccount {
    address: Address
    owner: Address
    mint: Address
    amount: bigint
}

export interface WalletDatasourceContext {
    slot: number
}

export interface WalletDatasourceTransaction {
    slot: number
    signature: Signature
    accounts: IAccountMeta[]
    instructions: RawInstruction[]
    logs: string[]
    preBalances: Lamports[]
    postBalances: Lamports[]
    preTokenBalances?: TokenBalance[]
    postTokenBalances?: TokenBalance[]
}

export type WalletDatasourceEvents = {
    account: (wallet: Address, account: Account<Uint8Array>, context: WalletDatasourceContext) => void
    tokenAccount: (wallet: Address, account: Account<Uint8Array>, context: WalletDatasourceContext) => void
    transaction: (wallet: Address, transaction: WalletDatasourceTransaction, context: WalletDatasourceContext) => void
}

export type WalletDatasource = Emitter<WalletDatasourceEvents, true> & {
    subscribe(): Promise<void>
}
