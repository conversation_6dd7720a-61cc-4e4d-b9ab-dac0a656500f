import { config } from '../config'
import { RpcServer } from '../modules/rpc-server/server'
import { registerRoutes } from '../modules/rpc-server/utils/routes'
import { routesPath } from '../utils/path'
import { createChildLogger } from './logger'

export const logger = createChildLogger('core:rpc-server')
export const rpcServer = new RpcServer(config.rpcServer.host, config.rpcServer.port, config.rpcServer)

export async function initializeRpcServer() {
    const stop = logger.createLoading().start('Initializing RPC server...')

    await registerRoutes(routesPath(), rpcServer).then(() => {
        stop('RPC server is initialized!')
    })
}

export async function startRpcServer() {
    await rpcServer.start().catch((error) => {
        throw new Error(`Failed to start RPC server:`, { cause: error })
    })
}
