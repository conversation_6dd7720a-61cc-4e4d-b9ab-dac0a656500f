import type { BlockhashLifetime } from '../modules/transaction/types'
import { highlight } from '@kdt310722/logger'
import { isNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { shorten } from '@kdt310722/utils/string'
import { blockhash } from '@solana/kit'
import { BLOCKHASH_LIFETIME, MAX_VALID_SLOT_GAP } from '../constants'
import { geyserSlot, isGeyserEnabled } from './geyser'
import { createChildLogger } from './logger'
import { rpcClient } from './rpc-client'
import { rpcWebsocketClient } from './rpc-websocket-client'

const logger = createChildLogger('core:block')

export interface BlockhashLifetimeWithSlot extends BlockhashLifetime {
    slot: number
}

let latestBlockhashLifetime: BlockhashLifetimeWithSlot | undefined

export function getBlockhashLifetime() {
    return latestBlockhashLifetime!
}

const handlers = new Set<(blockhashLifetime: BlockhashLifetimeWithSlot) => void>()

export function onBlockhashLifetimeUpdate(handler: (blockhashLifetime: BlockhashLifetimeWithSlot) => void) {
    handlers.add(handler)
}

export function setBlockhashLifetime(blockhashLifetime: BlockhashLifetimeWithSlot) {
    return tap(latestBlockhashLifetime = blockhashLifetime, () => {
        for (const handler of handlers) {
            handler(blockhashLifetime)
        }
    })
}

let updatePromise: Promise<BlockhashLifetimeWithSlot> | undefined

export async function updateBlockhashLifetimeUsingRpc() {
    return updatePromise ??= rpcClient.getLatestBlockhash({ commitment: 'finalized' }).send().then(({ context: { slot }, value }) => setBlockhashLifetime({ ...value, slot: Number(slot) })).finally(() => updatePromise = undefined)
}

geyserSlot.on('confirmedSlot', ({ slot, blockhash: blockhash_, blockHeight }) => {
    setBlockhashLifetime({ slot, blockhash: blockhash(blockhash_), lastValidBlockHeight: BigInt(blockHeight + BLOCKHASH_LIFETIME) })
})

export function isRpcBlockhashOutdated(slot: number) {
    const currSlot = latestBlockhashLifetime?.slot

    if (isNullish(currSlot)) {
        return true
    }

    return slot - currSlot > MAX_VALID_SLOT_GAP
}

const getDebugMessage = ({ slot, blockhash, lastValidBlockHeight }: BlockhashLifetimeWithSlot) => `Blockhash: ${highlight(shorten(blockhash, 4))}, slot: ${highlight(slot)}, block height: ${highlight(lastValidBlockHeight)}`

export async function initializeBlock() {
    const stop = logger.createLoading().start('Initialize block module...')

    rpcWebsocketClient.onSlot((slot) => {
        if (isRpcBlockhashOutdated(slot)) {
            const logLevel = isGeyserEnabled() ? 'info' : 'debug'

            if (isNullish(updatePromise)) {
                logger.log(logLevel, 'RPC blockhash lifetime is outdated, updating...')
            }

            updateBlockhashLifetimeUsingRpc().then((r) => logger.log(logLevel, getDebugMessage(r))).catch((error) => logger.error('Failed to update RPC blockhash lifetime', error))
        }
    })

    await updateBlockhashLifetimeUsingRpc().then((r) => {
        stop(getDebugMessage(r))
    })
}
