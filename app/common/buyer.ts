import type { AggregatedBondingCurve } from '../modules/aggregator/types'
import type { Token } from '../modules/indexer/types/entities/token'
import type { Sender } from '../modules/transaction/senders/sender'
import type { DexScreenerPaymentTransaction } from '../types/tracker'
import { highlight, message } from '@kdt310722/logger'
import { isArray } from '@kdt310722/utils/array'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { BigIntMath, formatNanoseconds } from '@kdt310722/utils/number'
import { createDeferred, tap } from '@kdt310722/utils/promise'
import { shorten } from '@kdt310722/utils/string'
import { calculateTokenOut, getMaxSolCost } from '@kdt-sol/pumpfun-sdk'
import { type Address, address } from '@solana/kit'
import { config } from '../config'
import { MAX_VALID_SLOT_GAP } from '../constants'
import { onBlockhashLifetimeUpdate } from '../core/block'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { type MultipleSendersTransaction, sendMultipleSendersTransaction } from '../core/transaction'
import { getWallet } from '../core/wallet'
import { BuyFailedTransaction } from '../entities/buy-failed-transaction'
import { BuyTransaction as SuccessEntity } from '../entities/buy-transaction'
import { formatPumpfunToken } from '../utils/formatters/format-pumpfun-token'
import { formatSol } from '../utils/formatters/format-sol'
import { aggregator } from './aggregator'
import { confirmTransaction, type ConfirmTransactionOptions, type ConfirmTransactionResult, type ConfirmTransactionResultError, type ConfirmTransactionResultSuccess, isConfirmTransactionResultError } from './confirm-transaction'
import { filter } from './filter'
import { getGlobalAccount } from './indexer'
import { notification } from './notification'
import { buildBuyTransaction } from './pumpfun'

const logger = createChildLogger('common:buyer')

export interface BuyToken {
    mint: Address
    info: Token
    bondingCurve: AggregatedBondingCurve
}

export interface BuyTransaction extends MultipleSendersTransaction {
    sortKey: bigint
    processTime: bigint
    bondingCurveSlot: number
}

export const availableToBuyTokens = new Set<string>()
export const buyTransactions: Record<string, BuyTransaction> = {}

export function cacheBuyTransaction(mint: string, bondingCurve_?: AggregatedBondingCurve) {
    if (!config.buy.cache) {
        return
    }

    const bondingCurve = bondingCurve_ ?? aggregator.get(mint)?.bondingCurve

    if (notNullish(bondingCurve)) {
        createBuyTransaction(address(mint), bondingCurve, config.buy).then((tx) => (buyTransactions[mint] = tx)).catch((error) => {
            logger.error(`Failed to create buy transaction for mint ${highlight(mint)}`, error, { bondingCurve })
        })
    }
}

export function removeCachedBuyTransaction(mint: string, reason: string) {
    if (mint in buyTransactions) {
        delete buyTransactions[mint]
        logger.debug(message(() => `Removed cached buy transaction for mint ${highlight(mint)}: ${highlight(reason)}`))
    }
}

onBlockhashLifetimeUpdate(({ slot }) => {
    for (const [mint, { lifetimeConstraint }] of Object.entries(buyTransactions)) {
        if (slot - MAX_VALID_SLOT_GAP > lifetimeConstraint.slot) {
            removeCachedBuyTransaction(mint, 'blockhash lifetime expired')
        }
    }
})

aggregator.on('removed', (mints) => {
    for (const mint of mints) {
        availableToBuyTokens.delete(mint)
        removeCachedBuyTransaction(mint, 'removed from aggregator')
    }
})

export function handleTokenAccountUpdate({ mint }: { mint: string }) {
    removeCachedBuyTransaction(mint, 'token account updated')
}

export async function initializeBuyer() {
    const wallet = await Promise.resolve().then(() => getWallet())

    wallet.tokenAccount.on('add', handleTokenAccountUpdate)
    wallet.tokenAccount.on('update', handleTokenAccountUpdate)
    wallet.tokenAccount.on('remove', handleTokenAccountUpdate)
}

export interface CreateBuyTransactionParams {
    amount: bigint
    slippage: number
    priorityFee: bigint
    tip: bigint
    antiMev?: boolean
}

export async function createBuyTransaction(mint: Address, bondingCurve: AggregatedBondingCurve, { amount, slippage, priorityFee, tip, antiMev = true }: CreateBuyTransactionParams) {
    const start = process.hrtime.bigint()
    const { feeRecipient, feeBasisPoints, creatorFeeBasisPoints } = getGlobalAccount()
    const tokenAmount = calculateTokenOut(bondingCurve, amount, bondingCurve.feeBasisPoints ?? feeBasisPoints, bondingCurve.creatorFeeBasisPoints ?? creatorFeeBasisPoints)
    const maxSolCost = getMaxSolCost(amount, slippage)

    return buildBuyTransaction({ mint, feeRecipient, amount: tokenAmount, maxSolCost, priorityFee, tip, creator: address(bondingCurve.creator), antiMev }).then((tx) => ({ ...tx, sortKey: bondingCurve.sortKey, bondingCurveSlot: bondingCurve.slot, processTime: process.hrtime.bigint() - start }))
}

filter.on('filter', (mint, current) => {
    if (current.includes(config.buy.filter)) {
        availableToBuyTokens.add(mint)
        cacheBuyTransaction(mint)
    } else {
        availableToBuyTokens.delete(mint)
        removeCachedBuyTransaction(mint, 'filtered out')
    }
})

export function getBuyToken(mint: string): BuyToken {
    const aggregated = aggregator.get(mint)

    if (isNullish(aggregated) || isNullish(aggregated.bondingCurve)) {
        throw new Error(`Bonding curve not found for mint: ${mint}`)
    }

    return { mint: address(mint), info: aggregated.token, bondingCurve: aggregated.bondingCurve }
}

export type ConfirmResult = ConfirmTransactionResult & { sender: Sender, sendTime: bigint }

export type ConfirmResultSuccess = Exclude<ConfirmResult, ConfirmTransactionResultError>

export type ConfirmResultError = Exclude<ConfirmResult, ConfirmTransactionResultSuccess>

export interface BuyOptions {
    useCache?: boolean
    params?: CreateBuyTransactionParams
    paymentTransaction?: DexScreenerPaymentTransaction
}

export type BuyerEvents = {
    'bought': (entity: SuccessEntity) => void
    'failed': (entity: BuyFailedTransaction) => void
}

export const buyer = new Emitter<BuyerEvents, true>()

buyer.on('bought', ({ tokenInfo, receivedTokens, balanceBefore, balanceAfter, sender, confirmationTime }) => {
    logger.info(`Bought ${highlight(formatPumpfunToken(receivedTokens))} ${highlight(tokenInfo.symbol)} (cost: ${highlight(formatSol(BigIntMath.abs(balanceAfter - balanceBefore)))}, sender: ${highlight(sender)}, confirmation time: ${highlight(formatNanoseconds(BigInt(confirmationTime * 1e6)))})`)
})

buyer.on('failed', ({ tokenInfo }) => {
    logger.warn(`Failed to buy token: ${highlight(tokenInfo.symbol)}`)
})

export async function handleBuyResult(token: BuyToken, result: ConfirmResultSuccess | ConfirmResultError[], processTime: bigint, params: CreateBuyTransactionParams, buyAt: Date, isUsingCached: boolean, bondingCurveSlot: number, paymentTransaction?: DexScreenerPaymentTransaction, processTimeUntilSend?: number) {
    const sendNotify = tap((transaction: SuccessEntity | BuyFailedTransaction) => {
        notification.sendBuyNotification({ buyAt, transaction, isUsingCached, bondingCurveSlot, paymentTransaction, processTimeUntilSend })
    })

    if (!isArray(result)) {
        return database.getRepository(SuccessEntity).save(SuccessEntity.fromBuyResult(token, result, processTime, params)).then(tap((r) => buyer.emit('bought', r))).then(sendNotify)
    }

    return database.getRepository(BuyFailedTransaction).save(BuyFailedTransaction.fromBuyResult(token, result, processTime, params)).then(tap((r) => buyer.emit('failed', r))).then(sendNotify)
}

export async function buy(mint: string, { useCache = true, params = config.buy, paymentTransaction }: BuyOptions = {}) {
    const buyAt = new Date()
    const start = process.hrtime.bigint()
    const token = getBuyToken(mint)

    let transaction: BuyTransaction
    let isCached = false

    if (useCache && mint in buyTransactions && buyTransactions[mint].sortKey >= token.bondingCurve.sortKey) {
        transaction = buyTransactions[mint]
        isCached = true
    } else {
        transaction = await createBuyTransaction(token.mint, token.bondingCurve, params)
    }

    Promise.resolve().then(() => logger.info(`Buying token ${highlight(token.info.symbol)}${isCached ? ' using cache' : ''}...`))

    const processTime = process.hrtime.bigint() - start
    const errors: ConfirmResultError[] = []
    const confirmResult = createDeferred<ConfirmResultSuccess | ConfirmResultError[]>()
    const promises: Array<Promise<ConfirmResult | Error>> = []
    const sendStart = process.hrtime.bigint()

    const handleConfirmResult = (sender: Sender, result: ConfirmTransactionResult, sendTime: bigint) => {
        const r = { sender, ...result, sendTime }

        logger.debug(message(() => `Transaction ${highlight(shorten(result.signature, 4))} confirmed (sender: ${highlight(sender.name)}, took: ${highlight(formatNanoseconds(BigInt(result.took * 1e6)))})`))

        if (isConfirmTransactionResultError(r)) {
            errors.push(r)
        } else if (!confirmResult.isSettled) {
            confirmResult.resolve(r)
        }

        return r
    }

    let processTimeUntilSend: number | undefined

    if (notNullish(paymentTransaction)) {
        processTimeUntilSend = Date.now() - paymentTransaction.receivedAt
    }

    await sendMultipleSendersTransaction(transaction, (signature, sender) => {
        const sendTime = process.hrtime.bigint() - sendStart
        const params: ConfirmTransactionOptions = { commitment: 'confirmed', maxWaitTime: config.buy.timeout }

        logger.debug(message(() => `Sent buy transaction to ${highlight(sender.name)}${isCached ? ' using cache' : ''}: ${highlight(shorten(signature, 4))} (took: ${highlight(formatNanoseconds(sendTime))})`))
        promises.push(confirmTransaction(signature, params).then((r) => handleConfirmResult(sender, r, sendTime)).catch((error) => (error instanceof Error ? error : new Error(`Confirm transaction sent using sender ${sender.name} failed`, { cause: error }))))
    })

    Promise.all(promises).then((result) => {
        if (!confirmResult.isSettled) {
            if (result.every((i) => i instanceof Error)) {
                confirmResult.reject(new AggregateError(result, 'Failed to confirm transaction'))
            } else {
                confirmResult.resolve(errors)
            }
        }
    })

    return confirmResult.then((result) => handleBuyResult(token, result, processTime, params, buyAt, isCached, transaction.bondingCurveSlot, paymentTransaction, processTimeUntilSend))
}
