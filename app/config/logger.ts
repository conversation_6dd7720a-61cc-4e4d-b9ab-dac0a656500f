import { LOG_LEVEL_NAMES, LogLevel, type LogLevelName } from '@kdt310722/logger'
import { isInProduction } from '@kdt310722/utils/node'
import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { logsPath } from '../utils/path'
import { bool } from '../utils/schemas/bool'

const logLevel = z.union([z.nativeEnum(LogLevel), z.enum(Object.values(LOG_LEVEL_NAMES) as [LogLevelName, ...LogLevelName[]])])

const debug = z.object({
    level: logLevel.default(LogLevel.DEBUG),
    filter: z.string().default('-*'),
})

const logDebug = z.union([debug, z.string()]).default({}).transform((value) => (
    isString(value) ? debug.parse({ filter: value }) : value
))

const file = z.object({
    enabled: bool.default(true),
    level: logLevel.default(LogLevel.ERROR),
    path: z.string().default(logsPath()),
})

const telegram = z.object({
    enabled: bool.default(true),
    level: logLevel.default(LogLevel.ERROR),
    token: z.string(),
    chatId: z.union([z.string(), z.number()]),
})

const schema = z.object({
    name: z.string().optional(),
    level: logLevel.default(isInProduction() ? LogLevel.INFO : LogLevel.TRACE),
    file: file.default({}),
    telegram: telegram.optional(),
    showName: bool.default(false),
    debug: logDebug,
})

export const logger = schema.default({})
