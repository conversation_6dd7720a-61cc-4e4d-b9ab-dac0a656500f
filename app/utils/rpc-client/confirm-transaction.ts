import type { Commitment, Signature } from '@solana/kit'
import type { ConfirmTransactionOptions, ConfirmTransactionResult } from '../../types/transactions'
import { createDeferred, poll } from '@kdt310722/utils/promise'
import { rpcClient } from '../../core/rpc-client'
import { onAborted } from '../abort'

export interface RpcConfirmTransactionOptions extends ConfirmTransactionOptions {
    commitment?: Commitment
    delay?: number
}

export async function confirmTransactionUsingRpc(signature: Signature, { commitment = 'confirmed', delay = 0, maxWaitTime = 60 * 1000, signal }: RpcConfirmTransactionOptions = {}) {
    const result = createDeferred<ConfirmTransactionResult>()

    const confirm = async () => {
        try {
            const { value } = await rpcClient.getSignatureStatuses([signature], { searchTransactionHistory: true }).send({ abortSignal: signal })

            if ((value[0]?.confirmationStatus === commitment || value[0]?.confirmationStatus === 'finalized') && !result.isSettled) {
                result.resolve({ slot: Number(value[0].slot), signature, err: value[0].err ?? undefined })
            }
        } catch (error) {
            if (!result.isSettled) {
                result.reject(error)
            }
        }
    }

    const timer = setTimeout(() => !result.isSettled && result.reject(new Error('Transaction confirmation timeout')), maxWaitTime)
    const stopAbortHandler = onAborted((error) => !result.isSettled && result.reject(error), signal)
    const stop = poll(confirm, delay, true)

    return result.finally(() => {
        stop()
        stopAbortHandler()
        clearTimeout(timer)
    })
}
