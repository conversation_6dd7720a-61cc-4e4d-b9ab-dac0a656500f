import { config } from '../config'
import { type BlockhashLifetimeWithSlot, onBlockhashLifetimeUpdate } from '../core/block'
import { geyserSlot } from '../core/geyser'
import { rpcServer } from '../core/rpc-server'
import { rpcWebsocketClient } from '../core/rpc-websocket-client'
import { indexer } from './indexer'

interface ClientStats {
    slot: number
    slotDiff: number
    latency: number
}

interface ServerStats {
    highestSlot?: number
    rpc: ClientStats
    geyser: ClientStats
    indexer: ClientStats
    blockhashLifetime?: BlockhashLifetimeWithSlot
}

export const emptyClientStats: ClientStats = { slot: 0, slotDiff: 0, latency: 0 }
export const stats: ServerStats = { rpc: { ...emptyClientStats }, geyser: { ...emptyClientStats }, indexer: { ...emptyClientStats } }

rpcWebsocketClient.onSlot((slot) => stats.rpc.slot = slot)
rpcWebsocketClient.onLatency((latency) => stats.rpc.latency = latency)
geyserSlot.on('slot', ({ slot }) => stats.geyser.slot = slot)
geyserSlot.on('latency', (latency) => stats.geyser.latency = latency)
indexer.notifications.on('slot', (slot) => stats.indexer.slot = slot)

onBlockhashLifetimeUpdate((blockhashLifetime) => {
    stats.blockhashLifetime = blockhashLifetime
})

export function getServerStats() {
    const highestSlot = Math.max(stats.rpc.slot, stats.geyser.slot, stats.indexer.slot)

    stats.rpc.slotDiff = stats.rpc.slot - highestSlot
    stats.geyser.slotDiff = stats.geyser.slot - highestSlot
    stats.indexer.slotDiff = stats.indexer.slot - highestSlot

    return { highestSlot, ...stats }
}

rpcServer.ws.addEvent('monitor')

export async function initializeMonitor() {
    const updateStats = () => {
        rpcServer.ws.emit('monitor', getServerStats())
    }

    return Promise.resolve().then(() => setInterval(updateStats, config.rpcServer.statsInterval))
}
