import { JsonRpcError } from '@kdt310722/rpc'
import { isNullish, isTrueLike, notNullish } from '@kdt310722/utils/common'
import { BaseError } from '@kdt-sol/geyser-client'
import { EntityNotFoundError } from 'typeorm'
import { ZodError } from 'zod'
import { fromZodError } from 'zod-validation-error'

export class RpcRequestError extends BaseError {
    public declare readonly request?: unknown

    public static from(error: unknown, request?: unknown) {
        if (error instanceof RpcRequestError) {
            return error
        }

        return new RpcRequestError('RPC request error:', { cause: error }).withRequest(request)
    }

    public isImportant() {
        return !(this.cause instanceof JsonRpcError || this.cause instanceof ZodError || this.cause instanceof EntityNotFoundError)
    }

    public withRequest(request?: unknown) {
        if (isTrueLike(process.env.SHOW_REQUEST_ON_RPC_ERROR)) {
            return this.withValue('request', request)
        }

        return this
    }

    public toJsonRpcError() {
        if (notNullish(this.cause)) {
            if (this.cause instanceof JsonRpcError) {
                return this.cause
            }

            if (this.cause instanceof ZodError) {
                return new JsonRpcError(-32_602, fromZodError(this.cause).toString())
            }
        }

        return new JsonRpcError(-32_600, isNullish(this.cause) || !(this.cause instanceof Error) ? 'An unknown error occurred' : this.cause.message, { cause: this.cause })
    }
}
