import type { SellConfig } from '../config/sell'
import type { SellOrder } from '../entities/sell-order'
import type { Token } from '../modules/indexer/types/entities/token'
import type { Sender } from '../modules/transaction/senders/sender'
import { highlight } from '@kdt310722/logger'
import { isNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { formatNanoseconds } from '@kdt310722/utils/number'
import { omit } from '@kdt310722/utils/object'
import { createDeferred } from '@kdt310722/utils/promise'
import { shorten } from '@kdt310722/utils/string'
import { calculateSolOut, getMinSolOut } from '@kdt-sol/pumpfun-sdk'
import { type Address, address } from '@solana/kit'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { type MultipleSendersTransaction, sendMultipleSendersTransaction } from '../core/transaction'
import { getWallet } from '../core/wallet'
import { SellFailedTransaction } from '../entities/sell-failed-transaction'
import { SellTransaction as SuccessEntity } from '../entities/sell-transaction'
import { formatPumpfunToken } from '../utils/formatters/format-pumpfun-token'
import { aggregator } from './aggregator'
import { confirmTransaction, type ConfirmTransactionResult, type ConfirmTransactionResultError, type ConfirmTransactionResultSuccess, isConfirmTransactionResultError } from './confirm-transaction'
import { getGlobalAccount } from './indexer'
import { notification } from './notification'
import { buildSellTransaction } from './pumpfun'

const logger = createChildLogger('common:sell')

export interface SellTransaction extends MultipleSendersTransaction {
    amount: bigint
    processTime: bigint
}

export interface SellParams extends Pick<SellConfig, 'slippage' | 'priorityFee' | 'tip' | 'antiMev' | 'timeout' | 'retry'> {
    amount?: bigint
    order?: SellOrder
    reason?: string
}

export async function createSellTransaction(mint: Address, params: SellParams) {
    const start = process.hrtime.bigint()
    const balance = getWallet().tokenAccount.get(mint)?.amount ?? 0n

    if (balance === 0n) {
        throw new Error('Not enough tokens to sell')
    }

    const bondingCurve = aggregator.get(mint)?.bondingCurve

    if (isNullish(bondingCurve)) {
        throw new Error(`No bonding curve found for token ${mint}`)
    }

    const { feeRecipient, feeBasisPoints, creatorFeeBasisPoints } = getGlobalAccount()
    const amount = params.amount ?? balance
    const minSolOutput = getMinSolOut(calculateSolOut(bondingCurve, amount, bondingCurve.feeBasisPoints ?? feeBasisPoints, bondingCurve.creatorFeeBasisPoints ?? creatorFeeBasisPoints), params.slippage)

    return buildSellTransaction({ ...params, mint, amount, feeRecipient, minSolOutput, creator: address(bondingCurve.creator) }).then((tx): SellTransaction => ({ ...tx, amount, processTime: process.hrtime.bigint() - start }))
}

export type SellConfirmResult = ConfirmTransactionResult & {
    mint: Address
    sender: Sender
    processTime: bigint
    sendTime: bigint
}

export type SellConfirmResultError = Exclude<SellConfirmResult, ConfirmTransactionResultSuccess>

export type SellConfirmResultSuccess = Exclude<SellConfirmResult, ConfirmTransactionResultError>

export type SellEvents = {
    sold: (entity: SuccessEntity) => void
    failed: (entity: SellFailedTransaction) => void
}

export const sellNotification = new Emitter<SellEvents, true>()

sellNotification.on('sold', (entity) => {
    logger.info(`Sell transaction ${highlight(shorten(entity.signature, 4))} succeeded (sender: ${highlight(entity.sender)}, confirmation time: ${highlight(formatNanoseconds(BigInt(entity.confirmationTime * 1e6)))})`)
})

export async function sell(token: Token, params: SellParams, retryCount = 0) {
    const sellAt = new Date()
    const mint = address(token.mint)
    const transaction = await createSellTransaction(mint, params)

    if (transaction.transactions.length !== 1) {
        throw Object.assign(new Error('Invalid transaction'), { transaction })
    }

    const timer = tap(logger.createTimer(), () => logger.info(`Selling ${highlight(`${formatPumpfunToken(transaction.amount)} ${token.symbol}`)} (attempts: ${highlight(retryCount + 1)})...`))
    const sendStart = process.hrtime.bigint()
    const senderPromise = createDeferred<Sender>()

    const [signature, sendTime, sender] = tap(await sendMultipleSendersTransaction(transaction, (_, sender_) => senderPromise.resolve(sender_)).then(async (signatures) => <const>[signatures[0], process.hrtime.bigint() - sendStart, await senderPromise]), ([signature]) => {
        logger.stopTimer(timer, 'info', `Sell transaction ${highlight(shorten(signature, 4))} sent, waiting for confirmation...`)
    })

    const confirmResult = await confirmTransaction(signature, { maxWaitTime: params.timeout })

    const sendNotify = (r: SuccessEntity | SellFailedTransaction) => tap(r, () => {
        notification.sendSellNotification({ sellAt, transaction: r, order: params.order, reason: params.reason })
    })

    if (isConfirmTransactionResultError(confirmResult)) {
        logger.info(`Sell transaction ${highlight(shorten(signature, 4))} failed`, omit(confirmResult, 'signature'))

        if (params.retry.enabled && retryCount < params.retry.attempts) {
            return await sell(token, params, retryCount + 1)
        }

        return database.getRepository(SellFailedTransaction).save(SellFailedTransaction.fromSellConfirmResult(token, transaction.amount, { ...confirmResult, mint, sender, sendTime, processTime: transaction.processTime }, params)).then((r) => tap(r, () => sellNotification.emit('failed', r))).then(sendNotify)
    }

    return database.getRepository(SuccessEntity).save(SuccessEntity.fromSellConfirmResult(token, transaction.amount, { ...confirmResult, mint, sender, sendTime, processTime: transaction.processTime }, params)).then((r) => tap(r, () => sellNotification.emit('sold', r))).then(sendNotify)
}
