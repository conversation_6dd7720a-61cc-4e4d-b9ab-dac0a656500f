import type { SellFailedTransaction } from '../../../entities/sell-failed-transaction'
import type { SellOrder } from '../../../entities/sell-order'
import { escapeMarkdownV2 } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { BigIntMath, formatNanoseconds, humanizeNumber } from '@kdt310722/utils/number'
import { formatDate } from '@kdt310722/utils/time'
import { calculateVirtualReservesBefore } from '@kdt-sol/pumpfun-sdk'
import { getMarketCap } from '../../../common/indexer'
import { SellTransaction } from '../../../entities/sell-transaction'
import { formatPumpfunToken } from '../../../utils/formatters/format-pumpfun-token'
import { formatSol } from '../../../utils/formatters/format-sol'
import { calculateProfitPercentage } from '../../../utils/prices'
import { tokenLink, txLink } from '../utils/messages'
import { getTokenHelpLinks } from './transaction'

export interface SellNotificationData {
    sellAt: Date
    transaction: SellTransaction | SellFailedTransaction
    order?: SellOrder
    reason?: string
}

export function getSellNotificationMessage({ sellAt, transaction, order, reason }: SellNotificationData, name?: string) {
    const message: string[] = []
    const token = transaction.tokenInfo

    if (transaction instanceof SellTransaction) {
        message.push(`💸 *Sell Alert* 💸`)
    } else {
        message.push(`❌ *Sell Failed* ❌`)
    }

    message.push('')

    if (notNullish(name)) {
        message.push(` \\- Name: *${name}*`)
    }

    message.push(
        ` \\- Token: *${tokenLink(token.mint, token.name)}*`,
        ` \\- Time: *${escapeMarkdownV2(formatDate(sellAt, true))}*`,
        ` \\- Amount: *${escapeMarkdownV2(`${formatPumpfunToken(transaction.amount)} ${token.symbol}`)}*`,
    )

    if (transaction instanceof SellTransaction) {
        const bondingCurveBefore = calculateVirtualReservesBefore({ ...transaction.bondingCurve, isBuy: false, solAmount: transaction.receivedSol, tokenAmount: transaction.tokensCost })
        const marketCapBefore = getMarketCap(bondingCurveBefore)
        const marketCapAfter = getMarketCap(transaction.bondingCurve)

        message.push(
            ` \\- Sell At MCap: *${escapeMarkdownV2(humanizeNumber(marketCapBefore))}*`,
            ` \\- MCap After Sell: *${escapeMarkdownV2(humanizeNumber(marketCapAfter))}*`,
        )
    }

    message.push(
        ` \\- Sender: *${transaction.sender}*`,
        ` \\- Slot: *${transaction.slot}*`,
        ` \\- Process Time: *${formatNanoseconds(transaction.processTime)}*`,
        ` \\- Send Time: *${formatNanoseconds(transaction.sendTime)}*`,
        ` \\- Confirmation Time: *${formatNanoseconds(BigInt(transaction.confirmationTime * 1e6))}*`,
    )

    if (notNullish(reason)) {
        message.push(` \\- Reason: *${reason}*`)
    }

    if (transaction instanceof SellTransaction) {
        const receivedSol = transaction.balanceAfter - transaction.balanceBefore
        const cost = notNullish(order) ? order.buyTransaction.balanceAfter - order.buyTransaction.balanceBefore : undefined

        message.push(` \\- Received: *${escapeMarkdownV2(formatSol(receivedSol))}*`)

        if (notNullish(cost)) {
            const profit = receivedSol + cost
            const profitPercentage = calculateProfitPercentage(BigIntMath.abs(cost), profit)

            message.push(` \\- Profit: *${escapeMarkdownV2(formatSol(profit))}* \\(*${escapeMarkdownV2(profitPercentage.toFixed(2))}%*\\)`)
        }
    } else {
        message.push(`\`\`\`json\n${escapeMarkdownV2(JSON.stringify(transaction.err, null, 2))}\`\`\``)
    }

    message.push('', `${txLink(transaction.signature, 'TX')} \\| ${getTokenHelpLinks(token.mint)}`)

    return message.join('\n')
}
