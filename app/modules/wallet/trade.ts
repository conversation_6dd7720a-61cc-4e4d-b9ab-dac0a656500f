import type { Address, Signature } from '@solana/kit'
import type { WalletDatasourceContext, WalletDatasourceTransaction } from './types'
import { Emitter } from '@kdt310722/utils/event'
import { createDeferred, type DeferredPromise, withTimeout } from '@kdt310722/utils/promise'
import { parseLogs, PumpEvent, type TradeEvent } from '@kdt-sol/pumpfun-sdk'
import { onAborted } from '../../utils/abort'

export type TradeEvents = {
    trade: (signature: Signature, data: TradeEvent, transaction: WalletDatasourceTransaction, context: WalletDatasourceContext) => void
}

export interface WaitTradeOptions {
    maxWaitTime?: number
    signal?: AbortSignal
}

export class Trade extends Emitter<TradeEvents, true> {
    protected readonly waiters: Record<Signature, Set<DeferredPromise<WalletDatasourceTransaction>>> = {}

    public async init() {
        await Promise.resolve()
    }

    public async wait(signature: Signature, { maxWaitTime = 60 * 1000, signal }: WaitTradeOptions = {}) {
        const promise = createDeferred<WalletDatasourceTransaction>()

        this.waiters[signature] ??= new Set()
        this.waiters[signature].add(promise)

        const stopAbort = onAborted((error) => !promise.isSettled && promise.reject(error), signal)

        return withTimeout(promise, maxWaitTime, 'Trade wait timeout').finally(() => {
            this.waiters[signature].delete(promise)

            if (this.waiters[signature].size === 0) {
                delete this.waiters[signature]
            }

            stopAbort()
        })
    }

    public handleTransaction(wallet: Address, transaction: WalletDatasourceTransaction, context: WalletDatasourceContext) {
        const events = parseLogs(transaction.logs)

        for (const event of events) {
            if (event.eventType === PumpEvent.TRADE && event.data.user === wallet) {
                this.emit('trade', transaction.signature, event.data, transaction, context)

                if (this.waiters[transaction.signature]?.size) {
                    for (const waiter of this.waiters[transaction.signature]) {
                        if (!waiter.isSettled) {
                            waiter.resolve(transaction)
                        }
                    }
                }
            }
        }
    }
}
