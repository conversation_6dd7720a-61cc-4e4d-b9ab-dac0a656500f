import type { Rpc<PERSON>ethod<PERSON>and<PERSON> } from '@kdt310722/rpc'
import type { RpcServer } from '../server'
import type { Route } from '../types/routes'
import { resolve } from 'node:path'
import { highlight, message } from '@kdt310722/logger'
import { isEmpty } from '@kdt310722/utils/common'
import { isFunction } from '@kdt310722/utils/function'
import { isKeyOf, isObject } from '@kdt310722/utils/object'
import { isString } from '@kdt310722/utils/string'
import { camelCase } from 'change-case'
import fg from 'fast-glob'
import { validateParams } from './params'

export function isValidRoute(module: unknown): module is Route {
    if (!isObject(module) || (isKeyOf(module, 'name') && !isString(module.name))) {
        return false
    }

    if (isKeyOf(module, 'handler')) {
        return isFunction(module.handler)
    }

    if (isKeyOf(module, 'httpHandler') && isKeyOf(module, 'wsHandler')) {
        return isFunction(module.httpHandler) && isFunction(module.wsHandler)
    }

    if (isKeyOf(module, 'httpHandler')) {
        return isFunction(module.httpHandler)
    }

    if (isKeyOf(module, 'wsHandler')) {
        return isFunction(module.wsHandler)
    }

    return false
}

export function getRoutes(module: unknown, namespace?: string): Route[] {
    if (isValidRoute(module)) {
        return [module]
    }

    if (isObject(module)) {
        if (isKeyOf(module, 'default') && isValidRoute(module.default)) {
            return [module.default]
        }

        const routes: Route[] = []

        for (const [name, item] of Object.entries(module)) {
            if (isValidRoute(item)) {
                if (isEmpty(item.name)) {
                    item.name = (namespace ? `${namespace}_` : '') + name
                }

                routes.push(item)
            }
        }

        return routes
    }

    return []
}

export function registerRoute(server: RpcServer, route: Route) {
    if (route.name) {
        if ('handler' in route) {
            const handler: RpcMethodHandler = (params, context) => route.handler(route.schema ? validateParams(route.schema, params) : params, context)

            server.http.addMethod(route.name, handler)
            server.ws.addMethod(route.name, handler)
        } else {
            if ('httpHandler' in route) {
                server.http.addMethod(route.name, (params, context) => route.httpHandler(route.schema ? validateParams(route.schema, params) : params, context))
            }

            if ('wsHandler' in route) {
                server.ws.addMethod(route.name, (params, context, client) => route.wsHandler(route.schema ? validateParams(route.schema, params) : params, context, client))
            }
        }

        server.logger.debug(message(() => `Registered route: ${highlight(route.name)}`))
    }
}

export function formatRouteName(name: string) {
    return camelCase(name.replaceAll(/-(\d)/g, '$1'))
}

export function getRouteName(path: string) {
    return path.slice(0, -3).split('/').map((i) => formatRouteName(i)).join('_')
}

export async function registerRoutes(path: string, server: RpcServer, namespace?: string) {
    const rootPath = resolve(path)
    const modules = await fg(resolve(rootPath, '**/*.{js,ts}')).then((files) => Promise.all(files.map(async (file) => [file, await import(file)])))

    for (const [file, module] of modules) {
        const routes = getRoutes(module, namespace).map((route) => ({ ...route }))

        if (routes.length === 1 && isEmpty(routes[0].name)) {
            routes[0].name = (namespace ? `${namespace}_` : '') + getRouteName(file.slice(rootPath.length + 1))
        }

        for (const route of routes) {
            registerRoute(server, route)
        }
    }
}
