import { context, highlight, LogLevel } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { GeyserClient } from '@kdt-sol/geyser-client'
import { config } from '../config'
import { getResubscribeEventsMessage } from '../utils/cli'
import { GeyserSlot } from '../utils/geyser-client/slot'
import { GeyserTransaction } from '../utils/geyser-client/transaction'
import { createChildLogger } from './logger'
import { rpcServer } from './rpc-server'
import { reloadWallet } from './wallet'

rpcServer.ws.addEvent('geyser:slot')
rpcServer.ws.addEvent('geyser:latency')

export const logger = createChildLogger('core:clients:geyser')
export const geyser = new GeyserClient(config.geyser.url, config.geyser)

export function isGrpcError(error: unknown): error is Error & { details: string } {
    return error instanceof Error && 'details' in error
}

export function isGeyserEnabled() {
    return config.geyser.enabled
}

let isInitialized = false

geyser.on('connect', () => isInitialized && tap(logger.createTimer('geyser:connect'), () => logger.info('Connecting to geyser server...')))
geyser.on('connected', () => isInitialized && logger.stopTimer('geyser:connect', 'info', 'Connected to geyser server!'))
geyser.on('error', (error) => logger.error(`Geyser client error${isGrpcError(error) ? `: ${highlight(error.details)}` : ''}`, context(() => (isGrpcError(error) ? [] : [error]))))
geyser.on('reconnect', (attempt, retriesLeft) => logger.info(`Reconnecting to geyser server (attempts: ${highlight(attempt)}, retries left: ${highlight(retriesLeft)})...`))
geyser.on('reconnectError', (error) => logger.warn(`Error while reconnecting to geyser server${error instanceof Error ? `: ${highlight(error.message)}` : ''}`, context(() => (error instanceof Error ? [] : [error]))))
geyser.on('reconnectFailed', (error) => logger.exit(1, 'fatal', 'Failed to reconnect to geyser server', error))
geyser.on('resubscribe', (subscriptions) => logger.info(`Resubscribing to ${highlight(Object.keys(subscriptions).length)} geyser subscriptions...${getResubscribeEventsMessage(subscriptions)}`))
geyser.on('ping', (id) => tap(logger.createTimer(`geyser:ping:${id}`), () => logger.debug(`Sending ping request ${highlight(`#${id}`)} to geyser server...`)))
geyser.on('pong', (id) => logger.stopTimer(`geyser:ping:${id}`, 'debug', `Received pong for ping request ${highlight(`#${id}`)} from geyser server`))

const updatedLogger = logger.child({ name: 'updated' })

geyser.on('updated', (request) => {
    updatedLogger.debug('Geyser stream updated', request)
})

geyser.on('resubscribed', (subscriptions) => {
    logger.info(`Resubscribed to ${highlight(Object.keys(subscriptions).length)} geyser subscriptions!`)
    Promise.resolve().then(() => reloadWallet())
})

export const geyserSlot = new GeyserSlot(geyser, config.geyser)
export const geyserTransaction = new GeyserTransaction(geyser)

geyser.on('unhandledMessage', (message) => {
    if (!geyserTransaction.isPendingUnsubscribeConfirmTransactionNotification(message)) {
        logger.warn('Unhandled geyser message', message)
    }
})

geyserSlot.on('latency', (latency) => {
    rpcServer.ws.emit('geyser:latency', latency)
})

let slotTimer: NodeJS.Timeout | undefined

geyser.on('disconnected', (isExplicitly) => {
    clearTimeout(slotTimer)
    logger.log(isExplicitly ? LogLevel.INFO : LogLevel.WARN, 'Disconnected from geyser server!')
})

function checkHealth() {
    const timeout = config.geyser.maxTimeBetweenTwoSlots

    if (slotTimer) {
        clearTimeout(slotTimer)
    }

    slotTimer = setTimeout(() => geyser.isConnected && Promise.resolve(logger.error(`No geyser slots received after ${highlight(format(Math.floor(timeout / 1000)))} seconds`)).then(() => geyser.disconnect(false)), timeout)
}

geyserSlot.on('confirmedSlot', (slot) => {
    rpcServer.ws.emit('geyser:slot', slot)
    checkHealth()
})

geyserTransaction.on('error', (error) => {
    logger.error('Geyser transaction module error', error)
})

export async function initializeGeyserClient() {
    if (!isGeyserEnabled()) {
        return
    }

    const stop = logger.createLoading().start('Connecting to geyser server...')

    await geyser.connect().then(() => geyserSlot.subscribe()).then(() => (isInitialized = true)).then(() => {
        stop('Connected to geyser server!')
    })
}
