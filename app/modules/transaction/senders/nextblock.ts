import type { UrlLike } from '@kdt310722/rpc'
import type { ErrorOptions } from 'zod-validation-error'
import { isNumber } from '@kdt310722/utils/number'
import { isKeysOf, isObject } from '@kdt310722/utils/object'
import { isString } from '@kdt310722/utils/string'
import { type Address, type Base64EncodedWireTransaction, signature } from '@solana/kit'
import { NEXTBLOCK_ENDPOINTS, NEXTBLOCK_MINIMUM_TIP_AMOUNT, NEXTBLOCK_TIP_ACCOUNTS } from '../constants'
import { Sender, type SenderOptions } from './sender'

export interface NextBlockSenderOptions<TEndpoints extends Record<string, UrlLike> = typeof NEXTBLOCK_ENDPOINTS> extends SenderOptions {
    endpoints?: TEndpoints
}

export class NextBlockSender<TEndpoints extends Record<string, UrlLike> = typeof NEXTBLOCK_ENDPOINTS> extends Sender {
    public readonly name: string = 'NextBlock'
    public readonly tipWallets: Address[] = NEXTBLOCK_TIP_ACCOUNTS
    public readonly minimumTipAmount: bigint = NEXTBLOCK_MINIMUM_TIP_AMOUNT

    public constructor(endpoint: Extract<keyof TEndpoints, string>, apiKey: string, { endpoints, ...options }: NextBlockSenderOptions<TEndpoints> = {}) {
        super(endpoints ?? NEXTBLOCK_ENDPOINTS, endpoint, { ...options, headers: { ...options.headers, Authorization: apiKey } })
    }

    protected getSendRequestBody(serializedTransaction: Base64EncodedWireTransaction) {
        return { transaction: { content: serializedTransaction }, frontRunningProtection: false }
    }

    protected getSignatureFromSendResult(result: unknown, createError: (message: string, options?: ErrorOptions) => Error) {
        if (!isObject(result)) {
            throw createError('Invalid response')
        }

        if (isKeysOf(result, ['reason']) && isString(result.reason)) {
            throw createError(result.reason)
        }

        if (isKeysOf(result, ['code', 'message']) && isNumber(result.code) && isString(result.message)) {
            throw createError(`[${result.code}] ${result.message}`)
        }

        if (!isKeysOf(result, ['signature']) || !isString(result.signature)) {
            throw createError('Missing signature from response')
        }

        return signature(result.signature)
    }
}
