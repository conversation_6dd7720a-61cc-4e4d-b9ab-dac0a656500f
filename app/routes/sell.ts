import type { z } from 'zod'
import { isNullish } from '@kdt310722/utils/common'
import { aggregator } from '../common/aggregator'
import { sell } from '../common/sell'
import { config } from '../config'
import { schema as mintSchema } from './get-aggregated-token'

export const schema = mintSchema

export const handler = async ({ mint }: z.infer<typeof schema>) => {
    if (isNullish(config.sell)) {
        throw new Error('Sell is not enabled')
    }

    const token = aggregator.get(mint)?.token

    if (isNullish(token)) {
        throw new Error(`Token ${mint} not found`)
    }

    return sell(token, config.sell)
}
