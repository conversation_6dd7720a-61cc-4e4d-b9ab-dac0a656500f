import type { ErrorOptions } from 'zod-validation-error'
import { isJsonRpcErrorResponseMessage, isJsonRpcMessage, isJsonRpcResponseMessage, toJsonRpcError } from '@kdt310722/rpc'
import { isString } from '@kdt310722/utils/string'
import { type Address, type Base64EncodedWireTransaction, signature } from '@solana/kit'
import { Sender } from './sender'

export class Rpc<PERSON>ender extends Sender {
    public readonly name: string = 'RPC'
    public readonly tipWallets: Address[] = []
    public readonly minimumTipAmount: bigint = 0n

    protected getSendRequestBody(serializedTransaction: Base64EncodedWireTransaction) {
        return { jsonrpc: '2.0', id: 1, method: 'sendTransaction', params: [serializedTransaction, { encoding: 'base64', skipPreflight: true }] }
    }

    protected getSignatureFromSendResult(result: unknown, createError: (message: string, options?: ErrorOptions) => Error) {
        if (!isJsonRpcMessage(result) || !isJsonRpcResponseMessage(result)) {
            throw createError('Invalid response')
        }

        if (isJsonRpcErrorResponseMessage(result)) {
            throw createError('JSON RPC error:', { cause: toJsonRpcError(result.error) })
        }

        if (!isString(result.result)) {
            throw createError('Missing signature from response')
        }

        return signature(result.result)
    }
}
