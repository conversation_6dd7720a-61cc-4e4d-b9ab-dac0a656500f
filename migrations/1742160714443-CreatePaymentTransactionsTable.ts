import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreatePaymentTransactionsTable1742160714443 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "payment_transactions" ("id" SERIAL NOT NULL, "slot" integer NOT NULL, "signature" character varying NOT NULL, "payer" character varying NOT NULL, "recipient" character varying NOT NULL, "amount" json NOT NULL, "datasource" character varying NOT NULL, "received_at" integer NOT NULL, "block_time" integer, "node_time" integer, "tokens" json NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d32b3c6b0d2c1d22604cbcc8c49" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_cd6a35d3fc0f471d5bada3bbb5" ON "payment_transactions" ("slot") `)
        await queryRunner.query(`CREATE INDEX "IDX_66cb170a45d94f69912cd9c9b9" ON "payment_transactions" ("payer") `)
        await queryRunner.query(`CREATE INDEX "IDX_3217eddd975c980354273fe449" ON "payment_transactions" ("recipient") `)
        await queryRunner.query(`CREATE INDEX "IDX_30a531e7f031de7914e3fcf49c" ON "payment_transactions" ("datasource") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_30a531e7f031de7914e3fcf49c"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_3217eddd975c980354273fe449"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_66cb170a45d94f69912cd9c9b9"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_cd6a35d3fc0f471d5bada3bbb5"`)
        await queryRunner.query(`DROP TABLE "payment_transactions"`)
    }
}
