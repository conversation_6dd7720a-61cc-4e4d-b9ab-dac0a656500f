import { existsSync } from 'node:fs'
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'
import { isNullish } from '@kdt310722/utils/common'
import { isInMode } from '@kdt310722/utils/node'

let _rootPath: string | undefined

export const rootPath = (...paths: string[]) => {
    if (isNullish(_rootPath)) {
        _rootPath = resolve(dirname(fileURLToPath(import.meta.url)))

        while (!existsSync(resolve(_rootPath, 'package.json'))) {
            _rootPath = dirname(_rootPath)
        }
    }

    return resolve(_rootPath, ...paths)
}

export const appPath = (...paths: string[]) => rootPath(isInMode('build') ? 'dist' : 'app', ...paths)

export const storagePath = (...paths: string[]) => rootPath('storage', ...paths)

export const logsPath = (...paths: string[]) => storagePath('logs', ...paths)

export const routesPath = (...paths: string[]) => appPath('routes', ...paths)
