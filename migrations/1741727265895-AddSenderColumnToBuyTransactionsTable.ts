import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddSenderColumnToBuyTransactionsTable1741727265895 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "buy_transactions" ADD "sender" character varying NOT NULL`)
        await queryRunner.query(`CREATE INDEX "IDX_e4d869ab17cea570aad43aae17" ON "buy_transactions" ("sender") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_e4d869ab17cea570aad43aae17"`)
        await queryRunner.query(`ALTER TABLE "buy_transactions" DROP COLUMN "sender"`)
    }
}
