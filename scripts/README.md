# HTTP Keep-Alive Checker

A TypeScript script to test and analyze HTTP Keep-Alive support for any URL using the `undici` library.

## Features

- **Real Keep-Alive Testing**: Actually tests connection reuse, not just header inspection
- **Performance Measurement**: Measures response times and connection reuse efficiency
- **Detailed Analytics**: Provides comprehensive reports on keep-alive behavior
- **Connection Pool Management**: Uses `undici` Pool for optimal connection handling
- **Error Handling**: Robust error handling for various network conditions
- **CLI Interface**: Easy-to-use command-line interface with customizable options

## Usage

### Basic Usage

```bash
# Test a simple URL
tsx scripts/check-keepalive.ts https://httpbin.org/get

# Test local server
tsx scripts/check-keepalive.ts http://localhost:3000
```

### Advanced Usage

```bash
# Custom number of requests
tsx scripts/check-keepalive.ts https://example.com --requests 20

# Custom interval between requests
tsx scripts/check-keepalive.ts https://example.com --interval 500

# Custom timeout
tsx scripts/check-keepalive.ts https://example.com --timeout 10000

# Multiple options
tsx scripts/check-keepalive.ts https://api.example.com/health \
  --requests 15 \
  --interval 200 \
  --timeout 5000 \
  --connections 1
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--requests <number>` | Number of requests to make | 10 |
| `--interval <number>` | Interval between requests (ms) | 100 |
| `--timeout <number>` | Connection timeout (ms) | 30000 |
| `--connections <number>` | Max connections in pool | 1 |
| `--help, -h` | Show help message | - |

## Output Explanation

The script provides detailed output including:

### Real-time Progress
- Connection establishment notifications
- Individual request timings
- Connection reuse indicators

### Final Analysis Report
- **Keep-Alive Support**: Whether the server supports keep-alive
- **Connection Statistics**: New vs reused connections
- **Performance Metrics**: Response times and improvements
- **Error Summary**: Any errors encountered during testing

### Example Output

```
🔍 Checking Keep-Alive support for: https://httpbin.org/get
📊 Configuration:
   - Max requests: 10
   - Request interval: 100ms
   - Timeout: 30000ms
   - Max connections: 1

🚀 Starting 10 requests...
🔗 New connection established (total: 1)
📈 Request 1: 245.67ms (new connection)
📈 Request 2: 89.23ms (reused connection)
📈 Request 3: 91.45ms (reused connection)
...

============================================================
📋 KEEP-ALIVE ANALYSIS RESULTS
============================================================
🌐 URL: https://httpbin.org/get
✅ Keep-Alive Support: YES
🔄 Connection Reuse Count: 9
🔗 New Connections: 1
♻️  Reused Connections: 9
⏱️  Estimated Keep-Alive Timeout: ~200ms
📊 Total Successful Requests: 10
⚡ Average Response Time: 98.45ms

📈 PERFORMANCE ANALYSIS:
   First request: 245.67ms
   Subsequent avg: 89.23ms
   🚀 Performance improvement: 63.7%
============================================================
```

## How It Works

1. **Connection Pool**: Creates a `undici` Pool with single connection limit
2. **Event Monitoring**: Listens to pool events to track connection lifecycle
3. **Sequential Requests**: Makes multiple requests with controlled intervals
4. **Timing Analysis**: Measures response times and connection reuse patterns
5. **Result Analysis**: Determines keep-alive support based on actual behavior

## Technical Details

### Keep-Alive Detection Logic

The script determines keep-alive support by:
- Monitoring actual connection count vs request count
- Tracking connection reuse events
- Analyzing response time patterns (reused connections are typically faster)

### Performance Benefits

When keep-alive is working properly, you should see:
- Only one connection established for multiple requests
- Faster response times for subsequent requests
- Reduced connection overhead

## Use Cases

- **API Testing**: Verify your API supports keep-alive properly
- **Performance Analysis**: Measure keep-alive performance benefits
- **Server Configuration**: Test different server keep-alive settings
- **Load Testing Preparation**: Understand connection behavior before load testing
- **Debugging**: Troubleshoot connection-related issues

## Requirements

- Node.js >= 20.12.0
- TypeScript/tsx for execution
- `undici` library (already included in project dependencies)

## Error Handling

The script handles various error conditions:
- Connection timeouts
- Network errors
- Invalid URLs
- Server errors (4xx, 5xx)
- DNS resolution failures

All errors are logged and included in the final report for analysis.
