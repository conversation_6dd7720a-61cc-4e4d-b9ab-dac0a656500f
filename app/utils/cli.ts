import type { AnyObject } from '@kdt310722/utils/object'
import { EOL } from 'node:os'
import { isZodError, ParseConfigError } from '@kdt310722/config'
import { badge, highlight, text } from '@kdt310722/logger'
import { isTrueLike, notNullish } from '@kdt310722/utils/common'
import { fromZodError } from 'zod-validation-error'

export function showResubscribeEvents() {
    return isTrueLike(process.env.SHOW_RESUBSCRIBE_EVENTS)
}

export function getResubscribeEventsMessage(events: AnyObject) {
    return showResubscribeEvents() ? `\n  + ${(Object.values(events).map((i) => highlight(`${i.method}${notNullish(i.params) ? `: ${JSON.stringify(i.params)}` : ''}`)).join('\n  + '))}` : ''
}

export function printConfigError(error: unknown): never {
    if (error instanceof ParseConfigError && isZodError(error.cause)) {
        const err = fromZodError(error.cause, {
            maxIssuesInMessage: Number.POSITIVE_INFINITY,
            issueSeparator: '\n  + ',
            prefix: 'Invalid config:\n  + ',
            prefixSeparator: '',
            unionSeparator: '\nor ',
        })

        process.stderr.write(`${badge('Error')} ${text(err.message)}${EOL}`)
        process.exit(1)
    }

    throw error
}
