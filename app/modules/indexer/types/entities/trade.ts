export interface TradeMetadata {
    feeRecipient: string
    feeBasisPoints: bigint
    fee: bigint
    creator: string
    creatorFeeBasisPoints: bigint
    creatorFee: bigint
}

export interface Trade {
    slot: number
    sortKey: bigint
    transactionIndex: number
    signature: string
    eventIndex: number
    user: string
    mint: string
    isBuy: boolean
    solAmount: bigint
    tokenAmount: bigint
    virtualSolReserves: bigint
    virtualTokenReserves: bigint
    realSolReserves: bigint
    realTokenReserves: bigint
    tokenPriceBefore: bigint
    tokenPrice: bigint
    metadata?: TradeMetadata
    timestamp: Date
}

export type TradeApiResponse = Omit<Trade, 'timestamp'> & { timestamp: string }
