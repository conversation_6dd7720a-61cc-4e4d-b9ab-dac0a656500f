import type { Address, IInstruction } from '@solana/kit'
import type { PriorityFee } from '../modules/transaction/types'
import { notNullish } from '@kdt310722/utils/common'
import { type BuyInstructionParamsInput, createBuyInstruction, createSellInstruction, getAssociatedTokenAddress, type SellInstructionParamsInput } from '@kdt-sol/pumpfun-sdk'
import { getCreateAssociatedTokenInstruction } from '@solana-program/token'
import { config } from '../config'
import { buildMultipleSendersTransaction, primarySenderId } from '../core/transaction'
import { getWallet } from '../core/wallet'

export interface BaseTradeParams {
    priorityFee?: bigint
    tip?: bigint
    antiMev?: boolean
}

export type BuyParams = BaseTradeParams & Omit<BuyInstructionParamsInput, 'user' | 'tokenAccount'>

export type SellParams = BaseTradeParams & Omit<SellInstructionParamsInput, 'user' | 'tokenAccount'>

export async function prepareForTrade(mint: Address) {
    const wallet = getWallet()
    const tokenAccount = await getAssociatedTokenAddress(mint, wallet.signer.address)
    const shouldCreateTokenAccount = !wallet.tokenAccount.has(tokenAccount)
    const instructions: IInstruction[] = []

    if (shouldCreateTokenAccount) {
        instructions.push(getCreateAssociatedTokenInstruction({ mint, owner: wallet.signer.address, ata: tokenAccount, payer: wallet.signer }))
    }

    return { signer: wallet.signer, senders: shouldCreateTokenAccount ? undefined : [primarySenderId], tokenAccount, instructions }
}

export async function buildBuyTransaction(params: BuyParams) {
    const { mint, amount, maxSolCost, priorityFee, tip, antiMev } = params
    const { signer, senders, tokenAccount, instructions } = await prepareForTrade(mint)

    const buyInstruction = await createBuyInstruction({ mint, amount, maxSolCost, user: signer, tokenAccount, creator: params.creator })
    const fee: PriorityFee | undefined = notNullish(priorityFee) ? { units: config.computeUnits.buy, lamports: priorityFee } : undefined

    return buildMultipleSendersTransaction({ instructions: [...instructions, buyInstruction], priorityFee: fee, tip, senders, antiMev })
}

export async function buildSellTransaction(params: SellParams) {
    const { mint, amount, minSolOutput, priorityFee, tip, antiMev } = params
    const { signer, senders, tokenAccount, instructions } = await prepareForTrade(mint)

    const sellInstruction = await createSellInstruction({ mint, amount, minSolOutput, user: signer, tokenAccount, creator: params.creator })
    const fee: PriorityFee | undefined = notNullish(priorityFee) ? { units: config.computeUnits.sell, lamports: priorityFee } : undefined

    return buildMultipleSendersTransaction({ instructions: [...instructions, sellInstruction], priorityFee: fee, tip, senders, antiMev })
}
