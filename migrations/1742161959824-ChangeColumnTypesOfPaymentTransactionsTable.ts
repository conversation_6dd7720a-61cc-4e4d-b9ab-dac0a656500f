import type { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeColumnTypesOfPaymentTransactionsTable1742161959824 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "payment_transactions" DROP COLUMN "received_at"`)
        await queryRunner.query(`ALTER TABLE "payment_transactions" ADD "received_at" TIMESTAMP WITH TIME ZONE NOT NULL`)
        await queryRunner.query(`ALTER TABLE "payment_transactions" DROP COLUMN "node_time"`)
        await queryRunner.query(`ALTER TABLE "payment_transactions" ADD "node_time" TIMESTAMP WITH TIME ZONE`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "payment_transactions" DROP COLUMN "node_time"`)
        await queryRunner.query(`ALTER TABLE "payment_transactions" ADD "node_time" integer`)
        await queryRunner.query(`ALTER TABLE "payment_transactions" DROP COLUMN "received_at"`)
        await queryRunner.query(`ALTER TABLE "payment_transactions" ADD "received_at" integer NOT NULL`)
    }
}
