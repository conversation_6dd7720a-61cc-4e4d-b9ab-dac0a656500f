import type { LoadedAddresses, TransactionHeader } from '../../types/transactions'
import { isNullish, notNullish, type Nullable } from '@kdt310722/utils/common'
import { withRetry } from '@kdt310722/utils/promise'
import { AccountRole, type Address, type Commitment, type IAccountMeta, type Signature } from '@solana/kit'
import { config } from '../../config'
import { rpcClient } from '../../core/rpc-client'

export async function getTransaction(signature: Signature, commitment: Commitment = 'confirmed', throws = true, signal?: AbortSignal) {
    const execute = async () => rpcClient.getTransaction(signature, { maxSupportedTransactionVersion: 0, encoding: 'json', commitment }).send({ abortSignal: signal }).then((result) => {
        if (isNullish(result)) {
            throw new Error('Transaction not found')
        }

        return result
    })

    const isTransactionNotFoundError = (error: unknown) => error instanceof Error && error.message === 'Transaction not found'

    return withRetry(execute, { signal, shouldRetry: (error) => isTransactionNotFoundError(error), retries: config.rpcClient.http.retry.retries, delay: config.rpcClient.http.retry.delay }).catch((error) => {
        if (!throws && isTransactionNotFoundError(error)) {
            return null
        }

        throw error
    })
}

export type GetTransactionResponse = NonNullable<Awaited<ReturnType<typeof getTransaction>>>

export function getAccountRole(isWritable: boolean, isSigner: boolean) {
    if (isSigner) {
        return isWritable ? AccountRole.WRITABLE_SIGNER : AccountRole.READONLY_SIGNER
    }

    return isWritable ? AccountRole.WRITABLE : AccountRole.READONLY
}

export function getTransactionInstructions(transaction: GetTransactionResponse) {
    return [...transaction.transaction.message.instructions, ...(transaction.meta?.innerInstructions ?? []).flatMap((i) => i.instructions)]
}

export function getTransactionAccounts(accountKeys: Address[], header: TransactionHeader, loadedAddresses?: Nullable<LoadedAddresses>): IAccountMeta[] {
    const accounts = accountKeys.map((account, index): IAccountMeta => {
        const isWritable = index < header.numRequiredSignatures - header.numReadonlySignedAccounts || (index >= header.numRequiredSignatures && index < accountKeys.length - header.numReadonlyUnsignedAccounts)
        const isSigner = index < header.numRequiredSignatures

        return { address: account, role: getAccountRole(isWritable, isSigner) }
    })

    if (notNullish(loadedAddresses)) {
        accounts.push(
            ...loadedAddresses.writable.map((i) => ({ address: i, role: AccountRole.WRITABLE })),
            ...loadedAddresses.readonly.map((i) => ({ address: i, role: AccountRole.READONLY })),
        )
    }

    return accounts
}
