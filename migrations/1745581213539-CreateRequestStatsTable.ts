import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateRequestStatsTable1745581213539 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "request_stats" ("id" SERIAL NOT NULL, "sender" character varying NOT NULL, "endpoint" character varying NOT NULL, "request_id" integer NOT NULL, "build_time" bigint NOT NULL, "send_time" bigint NOT NULL, "parse_response_time" bigint NOT NULL, CONSTRAINT "PK_d696c4f1cfdd2f98992ff0f2424" PRIMARY KEY ("id"))`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "request_stats"`)
    }
}
