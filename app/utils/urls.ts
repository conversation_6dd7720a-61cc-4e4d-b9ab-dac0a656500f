import type { UrlLike } from '@kdt310722/rpc'
import { isArray } from '@kdt310722/utils/array'

import { transform } from '@kdt310722/utils/function'

export function appendQueryParams(urlString: UrlLike, params: Record<string, string | string[]>): string {
    const url = new URL(urlString)

    for (const [key, value] of Object.entries(params)) {
        if (isArray(value)) {
            for (const v of value) {
                url.searchParams.append(key, v)
            }
        } else {
            url.searchParams.append(key, value)
        }
    }

    return url.toString()
}

export interface ParsedUrl {
    origin: string
    path: string
}

export function parseUrl(url: UrlLike): ParsedUrl {
    return transform(new URL(url), (parsed) => ({ origin: parsed.origin, path: parsed.pathname + parsed.search + parsed.hash }))
}
