import type { Account, Address, KeyPairSigner, Lamports, Signature } from '@solana/kit'
import type { WalletDatasource, WalletDatasourceContext, WalletDatasourceTransaction } from './types'
import { LruSet } from '@kdt310722/utils/array'
import { isNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { TokenAccount } from './token-account'
import { Trade } from './trade'
import { getAccountBalance } from './utils/accounts'

export type WalletEvents = {
    balance: (balance: Lamports) => void
}

export class Wallet extends Emitter<WalletEvents, true> {
    public readonly trade: Trade
    public readonly tokenAccount: TokenAccount

    protected accountSlot?: number
    protected tokenAccountSlot?: number
    protected signatures = new LruSet<Signature>(1000)

    #balance?: Lamports

    public constructor(protected readonly datasources: WalletDatasource[], public readonly signer: KeyPairSigner) {
        super()

        this.trade = new Trade()
        this.tokenAccount = new TokenAccount(signer.address)

        for (const datasource of datasources) {
            this.registerEvents(datasource)
        }
    }

    public get balance() {
        return this.#balance!
    }

    public async init() {
        this.#balance = await getAccountBalance(this.signer.address)

        await this.tokenAccount.load()
        await this.trade.init()
    }

    protected handleAccount(wallet: Address, account: Account<Uint8Array>, { slot }: WalletDatasourceContext) {
        if (wallet === this.signer.address && (isNullish(this.accountSlot) || slot > this.accountSlot)) {
            this.accountSlot = slot
            this.emit('balance', this.#balance = account.lamports)
        }
    }

    protected handleTokenAccount(wallet: Address, account: Account<Uint8Array>, { slot }: WalletDatasourceContext) {
        if (wallet === this.signer.address && (isNullish(this.tokenAccountSlot) || slot > this.tokenAccountSlot)) {
            this.tokenAccountSlot = slot
            this.tokenAccount.handleAccountUpdate(account)
        }
    }

    protected handleTransaction(wallet: Address, transaction: WalletDatasourceTransaction, context: WalletDatasourceContext) {
        const { signature } = transaction

        if (this.signatures.has(signature)) {
            return
        }

        this.signatures.add(signature)

        if (wallet === this.signer.address) {
            this.trade.handleTransaction(wallet, transaction, context)
            this.tokenAccount.handleTransaction(transaction)
        }
    }

    protected registerEvents(datasource: WalletDatasource) {
        datasource.on('account', this.handleAccount.bind(this))
        datasource.on('tokenAccount', this.handleTokenAccount.bind(this))
        datasource.on('transaction', this.handleTransaction.bind(this))
    }
}
