import type { ConfirmTransactionOptions, ConfirmTransactionResult } from '../../types/transactions'
import { LruSet } from '@kdt310722/utils/array'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { isKeyOf, isObject } from '@kdt310722/utils/object'
import { abortable, createDeferred, type DeferredPromise, withTimeout } from '@kdt310722/utils/promise'
import { type GeyserClient, type SubscribeUpdate, txErrDecode } from '@kdt-sol/geyser-client'
import { onAborted } from '../abort'

export type GeyserTransactionEvents = {
    error: (error: unknown) => void
}

export class GeyserTransaction extends Emitter<GeyserTransactionEvents, true> {
    protected readonly pendingConfirmations: Record<string, DeferredPromise<ConfirmTransactionResult>> = {}

    protected subscriptionId = 0
    protected pendingUnsubscribes = new LruSet<string>(1000)

    public constructor(protected readonly client: GeyserClient) {
        super()
    }

    public isPendingUnsubscribeConfirmTransactionNotification(message: unknown) {
        return isObject(message) && isKeyOf(message, 'filters') && message.filters.length === 1 && this.pendingUnsubscribes.has(message.filters[0])
    }

    public async confirmTransaction(signature: string, signer: string, { maxWaitTime = 60 * 1000, signal }: ConfirmTransactionOptions = {}) {
        if (notNullish(this.pendingConfirmations[signature])) {
            return this.pendingConfirmations[signature]
        }

        const result = this.pendingConfirmations[signature] = createDeferred<ConfirmTransactionResult>()
        const id = ++this.subscriptionId
        const successId = `gct_s_${id}`
        const failedId = `gct_f_${id}`

        let handler: (subscriptionId: string, data: SubscribeUpdate) => void
        let stopAbort: (() => void) | undefined

        if (notNullish(signal)) {
            stopAbort = onAborted((error) => result.reject(error), signal)
        }

        this.client.on('data', handler = (subscriptionId, data) => {
            if ((subscriptionId === successId || subscriptionId === failedId) && notNullish(data.transactionStatus)) {
                const slot = Number(data.transactionStatus.slot)
                const error = data.transactionStatus.err?.err

                if (!result.isSettled) {
                    result.resolve({ slot, signature, err: notNullish(error) ? txErrDecode.decode(error) : undefined })
                }
            }
        })

        const params = { signature, accountInclude: [], accountExclude: [], accountRequired: [signer] }

        const subscribe = async () => Promise.all([
            this.client.subscribe('transactionsStatus', { ...params, failed: false }, { id: successId }),
            this.client.subscribe('transactionsStatus', { ...params, failed: true }, { id: failedId }),
        ])

        await abortable(subscribe(), signal).catch((error) => {
            if (!result.isSettled) {
                result.reject(error)
            }
        })

        return withTimeout(result, maxWaitTime, 'Transaction confirmation timeout').finally(() => {
            this.pendingUnsubscribes.add(successId)
            this.pendingUnsubscribes.add(failedId)

            this.client.off('data', handler)
            stopAbort?.()

            Promise.all([this.client.unsubscribe(successId), this.client.unsubscribe(failedId)]).catch((error) => {
                this.emit('error', error)
            })

            delete this.pendingConfirmations[signature]
        })
    }
}
