import type { SubscribeUpdate } from '@kdt-sol/geyser-client'
import type { WalletDatasourceTransaction } from '../../../modules/wallet/types'
import type { LoadedAddresses } from '../../../types/transactions'
import type { RawInstruction } from '../../rpc-client/instructions'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { address, getAddressDecoder, lamports, signature, stringifiedBigInt, stringifiedNumber, type TokenBalance } from '@solana/kit'
import base58 from 'bs58'
import { getTransactionAccounts } from '../../rpc-client/transactions'

const addressDecoder = getAddressDecoder()

export type GeyserTokenBalance = NonNullable<NonNullable<NonNullable<SubscribeUpdate['transaction']>['transaction']>['meta']>['preTokenBalances'][0]

export const toSolanaTokenBalance = (input: GeyserTokenBalance): TokenBalance => ({
    mint: address(input.mint),
    owner: address(input.owner),
    programId: address(input.programId),
    accountIndex: input.accountIndex,
    uiTokenAmount: {
        amount: stringifiedBigInt(input.uiTokenAmount!.amount),
        uiAmount: input.uiTokenAmount!.uiAmount,
        uiAmountString: stringifiedNumber(input.uiTokenAmount!.uiAmountString),
        decimals: input.uiTokenAmount!.decimals,
    },
})

export const toWalletDatasourceTransaction = (slot: number, data: NonNullable<NonNullable<SubscribeUpdate['transaction']>['transaction']>): WalletDatasourceTransaction => {
    const loadedAddresses: LoadedAddresses = { readonly: [], writable: [] }

    if (notNullish(data.meta?.loadedReadonlyAddresses)) {
        loadedAddresses.readonly = data.meta.loadedReadonlyAddresses.map((i) => addressDecoder.decode(i))
    }

    if (notNullish(data.meta?.loadedWritableAddresses)) {
        loadedAddresses.writable = data.meta.loadedWritableAddresses.map((i) => addressDecoder.decode(i))
    }

    const message = data.transaction?.message
    const instructions = [...(message?.instructions ?? []), ...(data.meta?.innerInstructions.flatMap((i) => i.instructions) ?? [])].map((i): RawInstruction => ({ programIdIndex: i.programIdIndex, accounts: [...i.accounts.values()], data: i.data }))
    const accounts = isNullish(message?.header) ? [] : getTransactionAccounts(message.accountKeys.map((i) => addressDecoder.decode(i)), message.header, loadedAddresses)
    const preBalances = data.meta?.preBalances.map((i) => lamports(BigInt(i))) ?? []
    const postBalances = data.meta?.postBalances.map((i) => lamports(BigInt(i))) ?? []
    const preTokenBalances = data.meta?.preTokenBalances.map(toSolanaTokenBalance) ?? []
    const postTokenBalances = data.meta?.postTokenBalances.map(toSolanaTokenBalance) ?? []

    return { slot, signature: signature(base58.encode(data.signature)), accounts, instructions, logs: data.meta?.logMessages ?? [], preBalances, postBalances, preTokenBalances, postTokenBalances }
}
