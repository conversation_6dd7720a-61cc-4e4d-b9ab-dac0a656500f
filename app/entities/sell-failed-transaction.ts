import type { SellConfirmResultError, SellParams } from '../common/sell'
import type { Token } from '../modules/indexer/types/entities/token'
import { type Address, type Signature, signature, type TransactionError } from '@solana/kit'
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { BigIntColumn } from '../utils/database/columns/bigint-column'
import { JsonColumn } from '../utils/database/columns/json-column'

@Entity()
export class SellFailedTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column('varchar')
    public declare mint: Address

    @JsonColumn()
    public declare tokenInfo: Token

    @BigIntColumn()
    public declare amount: bigint

    @JsonColumn()
    public declare config: SellParams

    @Column()
    public declare sender: string

    @Column()
    public declare slot: number

    @Column('varchar')
    public declare signature: Signature

    @JsonColumn()
    public declare err: TransactionError

    @BigIntColumn()
    public declare processTime: bigint

    @BigIntColumn()
    public declare sendTime: bigint

    @Column()
    public declare confirmationTime: number

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date

    public static fromSellConfirmResult(token: Token, amount: bigint, result: SellConfirmResultError, config: SellParams) {
        const entity = new SellFailedTransaction()

        entity.mint = result.mint
        entity.tokenInfo = token
        entity.amount = amount
        entity.config = config
        entity.sender = result.sender.name
        entity.slot = result.slot
        entity.signature = signature(result.signature)
        entity.err = result.err
        entity.processTime = result.processTime
        entity.sendTime = result.sendTime
        entity.confirmationTime = result.took

        return entity
    }
}
