import type { z } from 'zod'
import { highlight } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { format, isBigInt } from '@kdt310722/utils/number'
import { buyTransactions } from '../common/buyer'
import { config } from '../config'
import { buy } from '../config/buy'
import { logger } from '../core/logger'
import { formatSol } from '../utils/formatters/format-sol'

export const schema = buy.partial().strict()

export const handler = (params: z.infer<typeof schema>) => {
    const updated: Record<string, unknown> = {}

    for (const [key, value] of Object.entries(params)) {
        if (notNullish(value)) {
            config.buy[key] = value
            updated[key] = isBigInt(value) ? formatSol(value) : value
        }
    }

    for (const [key] of Object.entries(buyTransactions)) {
        delete buyTransactions[key]
    }

    return tap(config.buy, () => logger.info(`Updated buy config: ${highlight(JSON.stringify(updated))}, cached buy transactions: ${highlight(format(Object.keys(buyTransactions).length))}`))
}
