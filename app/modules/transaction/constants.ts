import { address } from '@solana/kit'

export const MICRO_LAMPORTS_PER_LAMPORT = 10n ** 6n

export const NEXTBLOCK_ENDPOINTS = <const>{
    fra: 'https://fra.nextblock.io/api/v2/submit',
    ny: 'https://ny.nextblock.io/api/v2/submit',
}

export const NEXTBLOCK_MINIMUM_TIP_AMOUNT = 1_000_000n

export const NEXTBLOCK_TIP_ACCOUNTS = [
    address('NextbLoCkVtMGcV47JzewQdvBpLqT9TxQFozQkN98pE'),
    address('NexTbLoCkWykbLuB1NkjXgFWkX9oAtcoagQegygXXA2'),
    address('NeXTBLoCKs9F1y5PJS9CKrFNNLU1keHW71rfh7KgA1X'),
    address('NexTBLockJYZ7QD7p2byrUa6df8ndV2WSd8GkbWqfbb'),
    address('neXtBLock1LeC67jYd1QdAa32kbVeubsfPNTJC1V5At'),
    address('nEXTBLockYgngeRmRrjDV31mGSekVPqZoMGhQEZtPVG'),
    address('NEXTbLoCkB51HpLBLojQfpyVAMorm3zzKg7w9NFdqid'),
    address('nextBLoCkPMgmG8ZgJtABeScP35qLa2AMCNKntAP7Xc'),
]

export const BLOX_ROUTE_ENDPOINTS = <const>{
    uk: 'https://uk.solana.dex.blxrbdn.com/api/v2/submit',
    ny: 'https://ny.solana.dex.blxrbdn.com/api/v2/submit',
    la: 'https://la.solana.dex.blxrbdn.com/api/v2/submit',
    fra: 'https://germany.solana.dex.blxrbdn.com/api/v2/submit',
    ams: 'https://amsterdam.solana.dex.blxrbdn.com/api/v2/submit',
    jp: 'https://tokyo.solana.dex.blxrbdn.com/api/v2/submit',
}

export const BLOX_ROUTE_TIP_ACCOUNTS = [
    address('HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY'),
    address('95cfoy472fcQHaw4tPGBTKpn6ZQnfEPfBgDQx6gcRmRg'),
]

export const BLOX_ROUTE_MINIMUM_TIP_AMOUNT = 1_000_000n

export const JITO_ENDPOINTS = <const>{
    mainnet: 'https://mainnet.block-engine.jito.wtf/api/v1/transactions',
    ams: 'https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/transactions',
    fra: 'https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/transactions',
    ny: 'https://ny.mainnet.block-engine.jito.wtf/api/v1/transactions',
    jp: 'https://tokyo.mainnet.block-engine.jito.wtf/api/v1/transactions',
    slc: 'https://slc.mainnet.block-engine.jito.wtf/api/v1/transactions',
}

export const JITO_MINIMUM_TIP_AMOUNT = 0n

export const JITO_TIP_ACCOUNTS = [
    address('ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49'),
    address('96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5'),
    address('DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh'),
    address('3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT'),
    address('ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt'),
    address('Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY'),
    address('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL'),
    address('HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe'),
]

export const TEMPORAL_ENDPOINTS = <const>{
    pitt: 'http://pit1.nozomi.temporal.xyz',
    ewr: 'http://ewr1.nozomi.temporal.xyz',
    ams: 'http://ams1.nozomi.temporal.xyz',
    fra: 'http://fra2.nozomi.temporal.xyz',
}

export const TEMPORAL_MINIMUM_TIP_AMOUNT = 1_000_000n

export const TEMPORAL_TIP_ACCOUNTS = [
    address('TEMPaMeCRFAS9EKF53Jd6KpHxgL47uWLcpFArU1Fanq'),
    address('noz3jAjPiHuBPqiSPkkugaJDkJscPuRhYnSpbi8UvC4'),
    address('noz3str9KXfpKknefHji8L1mPgimezaiUyCHYMDv1GE'),
    address('noz6uoYCDijhu1V7cutCpwxNiSovEwLdRHPwmgCGDNo'),
    address('noz9EPNcT7WH6Sou3sr3GGjHQYVkN3DNirpbvDkv9YJ'),
    address('nozc5yT15LazbLTFVZzoNZCwjh3yUtW86LoUyqsBu4L'),
    address('nozFrhfnNGoyqwVuwPAW4aaGqempx4PU6g6D9CJMv7Z'),
    address('nozievPk7HyK1Rqy1MPJwVQ7qQg2QoJGyP71oeDwbsu'),
    address('noznbgwYnBLDHu8wcQVCEw6kDrXkPdKkydGJGNXGvL7'),
    address('nozNVWs5N8mgzuD3qigrCG2UoKxZttxzZ85pvAQVrbP'),
    address('nozpEGbwx4BcGp6pvEdAh1JoC2CQGZdU6HbNP1v2p6P'),
    address('nozrhjhkCr3zXT3BiT4WCodYCUFeQvcdUkM7MqhKqge'),
    address('nozrwQtWhEdrA6W8dkbt9gnUaMs52PdAv5byipnadq3'),
    address('nozUacTVWub3cL4mJmGCYjKZTnE9RbdY5AP46iQgbPJ'),
    address('nozWCyTPppJjRuw2fpzDhhWbW355fzosWSzrrMYB1Qk'),
    address('nozWNju6dY353eMkMqURqwQEoM3SFgEKC6psLCSfUne'),
    address('nozxNBgWohjR75vdspfxR5H9ceC7XXH99xpxhVGt3Bb'),
]

export const ZERO_SLOT_ENDPOINTS = <const>{
    ny: 'http://ny.0slot.trade',
    fra: 'http://de.0slot.trade',
    fra1: 'http://de1.0slot.trade',
    ams: 'http://ams.0slot.trade',
}

export const ZERO_SLOT_MINIMUM_TIP_AMOUNT = 1_000_000n

export const ZERO_SLOT_TIP_ACCOUNTS = [
    address('Eb2KpSC8uMt9GmzyAEm5Eb1AAAgTjRaXWFjKyFXHZxF3'),
    address('FCjUJZ1qozm1e8romw216qyfQMaaWKxWsuySnumVCCNe'),
    address('ENxTEjSQ1YabmUpXAdCgevnHQ9MHdLv8tzFiuiYJqa13'),
    address('6rYLG55Q9RpsPGvqdPNJs4z5WTxJVatMB8zV3WJhs5EK'),
    address('Cix2bHfqPcKcM233mzxbLk14kSggUUiz2A87fJtGivXr'),
]
