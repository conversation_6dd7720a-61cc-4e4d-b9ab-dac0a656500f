export function computePercentageOf(amount: bigint, percentage: number, scaleFactor = 10_000) {
    return (amount * BigInt(percentage * scaleFactor)) / BigInt(100 * scaleFactor)
}

export function computeTargetProfit(solAmount: bigint, profitPercentage: number, scaleFactor?: number) {
    return solAmount + computePercentageOf(solAmount, profitPercentage, scaleFactor)
}

export function computeStopLoss(solAmount: bigint, lossPercentage: number, scaleFactor?: number) {
    return solAmount - computePercentageOf(solAmount, lossPercentage, scaleFactor)
}
