import type { IndexerClient } from '../indexer-client'
import type { Trade, TradeApiResponse } from '../types/entities/trade'
import type { GetParams } from '../types/params'
import { formatTrade } from '../utils/formatters'

export interface GetTradesParams extends Omit<GetParams, 'startSlot' | 'endSlot'> {
    startSortKey?: bigint
    endSortKey?: bigint
}

export async function getTrades(client: IndexerClient, { limit = 1000, ...params }: GetTradesParams = {}, offset = 0): Promise<Trade[]> {
    const trades = await client.call<TradeApiResponse[]>('getTrades', { ...params, count: false, limit, offset, sortDirection: 'ASC' }).then((trades) => trades.map(formatTrade))

    if (trades.length < limit) {
        return trades
    }

    return getTrades(client, { ...params, limit }, offset + limit).then((nextTrades) => [...trades, ...nextTrades])
}
