import type { UrlLike } from '@kdt310722/rpc'
import type { Address, Base64EncodedWireTransaction } from '@solana/kit'
import { BLOX_ROUTE_ENDPOINTS, BLOX_ROUTE_MINIMUM_TIP_AMOUNT, BLOX_ROUTE_TIP_ACCOUNTS } from '../constants'
import { NextBlockSender, type NextBlockSenderOptions } from './nextblock'

export class BloxRouteSender<TEndpoints extends Record<string, UrlLike> = typeof BLOX_ROUTE_ENDPOINTS> extends NextBlockSender<TEndpoints> {
    public override readonly name: string = 'BloxRoute'
    public override readonly tipWallets: Address[] = BLOX_ROUTE_TIP_ACCOUNTS
    public override readonly minimumTipAmount: bigint = BLOX_ROUTE_MINIMUM_TIP_AMOUNT

    public constructor(endpoint: Extract<keyof TEndpoints, string>, apiKey: string, { endpoints, ...options }: NextBlockSenderOptions<TEndpoints> = {}) {
        super(endpoint, apiKey, { endpoints: endpoints ?? (BLOX_ROUTE_ENDPOINTS as unknown as TEndpoints), ...options })
    }

    protected override getSendRequestBody(serializedTransaction: Base64EncodedWireTransaction) {
        return { transaction: { content: serializedTransaction }, skipPreFlight: true, frontRunningProtection: false, useStakedRPCs: true }
    }
}
