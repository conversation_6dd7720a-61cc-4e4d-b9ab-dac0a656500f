#!/usr/bin/env tsx

import express from 'express'

const app = express()
const port = 3001

// Enable keep-alive with custom settings
app.use((req, res, next) => {
    // Set keep-alive headers
    res.setHeader('Connection', 'keep-alive')
    res.setHeader('Keep-Alive', 'timeout=5, max=100')
    next()
})

// Simple endpoint for testing
app.get('/test', (req, res) => {
    const timestamp = new Date().toISOString()
    res.json({
        message: 'Keep-Alive test endpoint',
        timestamp,
        headers: req.headers,
        connection: {
            remoteAddress: req.connection.remoteAddress,
            remotePort: req.connection.remotePort
        }
    })
})

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Demo server for Keep-Alive testing',
        endpoints: ['/test', '/health'],
        keepAlive: {
            enabled: true,
            timeout: '5 seconds',
            maxRequests: 100
        }
    })
})

const server = app.listen(port, () => {
    console.log(`🚀 Demo server running at http://localhost:${port}`)
    console.log(`📋 Available endpoints:`)
    console.log(`   - GET /         - Server info`)
    console.log(`   - GET /test     - Keep-alive test endpoint`)
    console.log(`   - GET /health   - Health check`)
    console.log(``)
    console.log(`🔧 Keep-Alive settings:`)
    console.log(`   - Timeout: 5 seconds`)
    console.log(`   - Max requests per connection: 100`)
    console.log(``)
    console.log(`🧪 Test with:`)
    console.log(`   tsx scripts/check-keepalive.ts http://localhost:${port}/test`)
})

// Configure server keep-alive settings
server.keepAliveTimeout = 5000 // 5 seconds
server.headersTimeout = 6000   // 6 seconds (should be > keepAliveTimeout)

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...')
    server.close(() => {
        console.log('✅ Server closed')
        process.exit(0)
    })
})

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...')
    server.close(() => {
        console.log('✅ Server closed')
        process.exit(0)
    })
})
