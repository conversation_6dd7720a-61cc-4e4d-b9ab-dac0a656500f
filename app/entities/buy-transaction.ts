import type { Address, Signature } from '@solana/kit'
import type { BuyToken, ConfirmResultSuccess, CreateBuyTransactionParams } from '../common/buyer'
import type { AggregatedBondingCurve } from '../modules/aggregator/types'
import type { Token } from '../modules/indexer/types/entities/token'
import { pick } from '@kdt310722/utils/object'
import { parseLogs, PumpEvent } from '@kdt-sol/pumpfun-sdk'
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { getWallet } from '../core/wallet'
import { BigIntColumn } from '../utils/database/columns/bigint-column'
import { JsonColumn } from '../utils/database/columns/json-column'
import { getBalanceChange } from '../utils/transactions'

@Entity()
export class BuyTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column()
    public declare slot: number

    @Index()
    @Column('varchar')
    public declare signature: Signature

    @Index()
    @Column('varchar')
    public declare sender: string

    @Index()
    @Column('varchar')
    public declare mint: Address

    @JsonColumn()
    public declare tokenInfo: Token

    @BigIntColumn()
    public declare amount: bigint

    @Column()
    public declare slippage: number

    @BigIntColumn()
    public declare priorityFee: bigint

    @BigIntColumn()
    public declare tip: bigint

    @BigIntColumn()
    public declare solCost: bigint

    @BigIntColumn()
    public declare receivedTokens: bigint

    @BigIntColumn()
    public declare balanceBefore: bigint

    @BigIntColumn()
    public declare balanceAfter: bigint

    @JsonColumn()
    public declare bondingCurve: Omit<AggregatedBondingCurve, 'sortKey' | 'slot'>

    @BigIntColumn()
    public declare processTime: bigint

    @BigIntColumn()
    public declare sendTime: bigint

    @Column()
    public declare confirmationTime: number

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date

    public static getTradeEvent(result: Pick<ConfirmResultSuccess, 'logs'>) {
        const events = parseLogs(result.logs).filter((i) => i.eventType === PumpEvent.TRADE).toArray()

        if (events.length !== 1) {
            throw new Error(`Unexpected number of trade events: ${events.length}`)
        }

        return events[0].data
    }

    public static fromBuyResult(token: BuyToken, result: ConfirmResultSuccess, processTime: bigint, params: CreateBuyTransactionParams) {
        const entity = new BuyTransaction()
        const trade = this.getTradeEvent(result)

        if (!trade.isBuy) {
            throw new Error('Expected a buy trade event')
        }

        const { pre, post } = getBalanceChange(result, getWallet().signer.address)

        entity.slot = result.slot
        entity.sender = result.sender.name
        entity.signature = result.signature
        entity.mint = token.mint
        entity.tokenInfo = token.info
        entity.amount = params.amount
        entity.slippage = params.slippage
        entity.priorityFee = params.priorityFee
        entity.tip = params.tip
        entity.solCost = trade.solAmount
        entity.receivedTokens = trade.tokenAmount
        entity.balanceBefore = pre
        entity.balanceAfter = post
        entity.bondingCurve = pick(trade, 'virtualSolReserves', 'virtualTokenReserves', 'realSolReserves', 'realTokenReserves', 'creator', 'feeBasisPoints', 'creatorFeeBasisPoints')
        entity.processTime = processTime
        entity.sendTime = result.sendTime
        entity.confirmationTime = result.took

        return entity
    }
}
