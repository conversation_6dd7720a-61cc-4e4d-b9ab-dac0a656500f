import type { Trade } from '../../indexer/types/entities/trade'
import type { Aggregated } from '../types'
import { isNullish } from '@kdt310722/utils/common'
import { BigIntMath } from '@kdt310722/utils/number'
import { calculatePercentDrop, calculatePercentIncrease } from '../../../utils/prices'
import { toAggregatedBondingCurve, toBondingCurveAccount } from './formatters/to-aggregated-bonding-curve'
import { toAggregatedTokenStats } from './formatters/to-aggregated-token-stats'
import { toTokenStats } from './formatters/to-token-stats'
import { isNewTrade } from './trades'

export function aggregate(item: Aggregated, trade: Trade) {
    if (isNullish(item.stats)) {
        return toAggregatedTokenStats(toTokenStats(trade), toAggregatedBondingCurve(toBondingCurveAccount(trade)))
    }

    const result = { ...item.stats }

    if (isNewTrade(item.stats, trade)) {
        result.sortKey = trade.sortKey
        result.signature = trade.signature
        result.slot = trade.slot
        result.transactionIndex = trade.transactionIndex
        result.eventIndex = trade.eventIndex
        result.lastTrade = trade.timestamp
        result.price = trade.tokenPrice
    }

    if (trade.isBuy) {
        result.buyVolume += trade.solAmount
        result.buysCount += 1
    } else {
        result.sellVolume += trade.solAmount
        result.sellsCount += 1
    }

    result.atl = BigIntMath.min(result.atl, trade.tokenPrice)
    result.ath = BigIntMath.max(result.ath, trade.tokenPrice)
    result.upFromAtl = calculatePercentIncrease(result.atl, result.price)
    result.downFromAth = calculatePercentDrop(result.ath, result.price)

    return result
}
