import type { FetchOptions } from '@kdt310722/utils/node'
import type { RpcTransport } from '@solana/kit'
import type { TransportRequest } from './types'
import { isJsonRpcErrorResponseMessage, isJsonRpcMessage, isJsonRpcResponseMessage, toJsonRpcError } from '@kdt310722/rpc'
import { isNullish } from '@kdt310722/utils/common'
import { tryCatch } from '@kdt310722/utils/function'
import { fetch } from '@kdt310722/utils/node'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { withRetry } from '@kdt310722/utils/promise'
import PQueue from 'p-queue'
import { TransportHttpError } from '../../errors/transport-http-error'
import { shouldRetry } from '../errors'

export interface SolanaRpcTransportConfig extends FetchOptions {
    url: string
    headers?: Record<string, string>
    maxRequestPerSecond?: number
    onRequest?: (request: TransportRequest) => void
    onResponse?: (body: string, response: Response, request: TransportRequest) => void
}

export function createSolanaRpcTransport({ url, headers, timeout, retry = true, maxRequestPerSecond, onRequest, onResponse }: SolanaRpcTransportConfig): RpcTransport {
    const retryOptions = resolveNestedOptions(retry) || { enabled: false }
    const limiter = new PQueue({ interval: 1000, intervalCap: maxRequestPerSecond })

    let requestId = 0

    const call = async ({ payload, signal }: Parameters<RpcTransport>[0]) => {
        const id = ++requestId
        const request: TransportRequest = { id, url, method: 'POST', headers: { 'Content-Type': 'application/json', ...headers }, body: JSON.stringify(payload) }

        onRequest?.(request)

        const response = await fetch(url, { ...request, signal }, { timeout, retry: false })
        const body = await response.text()
        const createError = (message: string) => new TransportHttpError(message).withRequest(request).withResponse(body, response)

        onResponse?.(body, response, request)

        if (!response.ok) {
            throw createError('Server returned a HTTP error')
        }

        const result = tryCatch(() => JSON.parse(body), null)

        if (isNullish(result)) {
            throw createError('Server returned an invalid JSON response')
        }

        if (!isJsonRpcMessage(result) || !isJsonRpcResponseMessage(result)) {
            throw createError('Server returned an invalid JSON-RPC response')
        }

        if (isJsonRpcErrorResponseMessage(result)) {
            throw Object.assign(toJsonRpcError(result.error), { request })
        }

        return result
    }

    const transport = async (config: Parameters<RpcTransport>[0]) => {
        if (!retryOptions.enabled) {
            return call(config)
        }

        return withRetry(async () => call(config), {
            ...retryOptions,
            onFailedAttempt: (error) => {
                if (!shouldRetry(error)) {
                    throw error
                }

                retryOptions.onFailedAttempt?.(Object.assign(error, { payload: config.payload }))
            },
        })
    }

    return (async (config) => await limiter.add(async () => transport(config))) as RpcTransport
}
