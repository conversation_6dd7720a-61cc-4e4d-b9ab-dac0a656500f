import type { Awaitable } from '@kdt310722/utils/promise'
import type { Request } from 'express'
import { context, highlight, type Logger, message } from '@kdt310722/logger'
import { JsonRpcError, type RpcClient, RpcWebSocketServer, type RpcWebSocketServerOptions } from '@kdt310722/rpc'
import { findAsync } from '@kdt310722/utils/array'
import { type BufferLike, toString } from '@kdt310722/utils/buffer'
import { transform } from '@kdt310722/utils/function'
import { stringifyJson } from '@kdt310722/utils/json'
import { type AnyObject, pick } from '@kdt310722/utils/object'
import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { createChildLogger } from '../../core/logger'
import { handleRequestError } from './utils/errors'
import { validateParams } from './utils/params'

export type WebsocketRpcClient<TContext extends AnyObject = AnyObject> = RpcClient<TContext & { subscriptions: Set<string> }>

export type EventName<TContext extends AnyObject = AnyObject> = (name: string, client: WebsocketRpcClient<TContext>) => Awaitable<boolean>

export type WebsocketRpcMethodHandler<TContext extends AnyObject = AnyObject, TParams = any, TResult = any> = (params: TParams, context: TContext, client: WebsocketRpcClient<TContext>) => Awaitable<TResult>

export interface WebsocketRpcServerOptions<TContext extends AnyObject = AnyObject> extends Omit<RpcWebSocketServerOptions<WebsocketRpcClient<TContext>>, 'methods'> {
    context?: TContext
}

export class WebsocketRpcServer<TContext extends AnyObject = AnyObject> {
    protected readonly logger: Logger
    protected readonly server: RpcWebSocketServer<WebsocketRpcClient<TContext>>
    protected readonly context: TContext

    protected readonly clients = new Map<number, WebsocketRpcClient<TContext>>()
    protected readonly events = new Set<EventName<TContext>>()
    protected readonly subscriptions = new Map<string, Set<number>>()

    public constructor(public readonly host: string, public readonly port: number, options: WebsocketRpcServerOptions = {}) {
        this.logger = createChildLogger('rpc-server:ws')
        this.server = this.createServer(host, port, options)
        this.context = options.context ?? {} as TContext

        this.addMethod('subscribe', this.handleSubscribe.bind(this))
        this.addMethod('unsubscribe', this.handleUnsubscribe.bind(this))
    }

    public async start() {
        return this.server.server.start()
    }

    public addMethod(name: string, handler: WebsocketRpcMethodHandler<TContext>, override = false) {
        this.server.addMethod(name, (params, client) => handler(params, client.metadata, client), override)
    }

    public addEvent(name: string | EventName<TContext>) {
        this.events.add(isString(name) ? (n) => n === name : name)
    }

    public emit(event: string, params: unknown) {
        const clients = this.subscriptions.get(event)

        for (const clientId of clients ?? []) {
            const client = this.clients.get(clientId)

            if (client && client.socket.readyState === client.socket.OPEN) {
                client.notify(event, params).catch((error) => this.logger.warn('Error while sending notification to client', { client: pick(client, 'id', 'ip') }, error))
            }
        }
    }

    protected async handleSubscribe(params: unknown, _: TContext, client: WebsocketRpcClient<TContext>) {
        const event = this.getEventName(params)
        const match = await findAsync([...this.events], async (name) => name(event, client))

        if (!match) {
            throw new JsonRpcError(-32_602, 'Event not found')
        }

        if (!this.subscriptions.has(event)) {
            this.subscriptions.set(event, new Set())
        }

        this.subscriptions.get(event)!.add(client.id)
        this.clients.get(client.id)!.metadata.subscriptions.add(event)

        return true
    }

    protected handleUnsubscribe(params: unknown, _: TContext, client: WebsocketRpcClient<TContext>) {
        const event = this.getEventName(params)

        this.clients.get(client.id)?.metadata.subscriptions.delete(event)
        this.subscriptions.get(event)?.delete(client.id)

        if (this.subscriptions.get(event)?.size === 0) {
            this.subscriptions.delete(event)
        }

        return true
    }

    protected getEventName(params: unknown) {
        return transform(validateParams(z.union([z.string().nonempty(), z.tuple([z.string().nonempty()])]), params), (value) => (isString(value) ? value : value[0]))
    }

    protected handleConnection(client: WebsocketRpcClient<TContext>) {
        client.metadata = { ...this.context, subscriptions: new Set() }

        this.clients.set(client.id, client)
        this.logger.debug(message(() => `New client connected: ${highlight(`#${client.id}`)} - ${highlight(client.ip)}`))

        client.socket.on('close', (code: number, reason: BufferLike) => {
            this.clients.delete(client.id)

            for (const event of client.metadata.subscriptions) {
                this.subscriptions.get(event)?.delete(client.id)
            }

            this.logger.debug(message(() => `Client ${highlight(`#${client.id}`)} disconnected`), context(() => [{ code, reason: toString(reason) }]))
        })
    }

    protected handleError(error: unknown, request?: Request) {
        return handleRequestError(this.logger, error, request)
    }

    protected createServer(host: string, port: number, options: WebsocketRpcServerOptions<TContext> = {}) {
        const ws = new RpcWebSocketServer<WebsocketRpcClient<TContext>>(host, port, { errorHandler: this.handleError.bind(this), dataEncoder: stringifyJson, ...options })

        ws.server.on('listening', () => this.logger.info(`RPC server is listening on ${highlight(`${ws.server.host}:${ws.server.port}`)}`))
        ws.server.on('close', () => this.logger.info(`RPC server is stopped!`))
        ws.server.on('error', (error) => this.logger.error('RPC server error', error))

        ws.on('error', (error) => this.logger[error instanceof JsonRpcError ? 'debug' : 'error']('RPC server error', error))
        ws.on('unhandledMessage', ({ id }, msg) => this.logger.debug(message(() => `Received unhandled message from client ${highlight(`#${id}`)}`), { message: msg }))
        ws.on('notification', ({ id }, method, params) => this.logger.debug(message(() => `Received a notification from client ${highlight(`#${id}`)}`), { method, params }))
        ws.on('connection', this.handleConnection.bind(this))

        return ws
    }
}
