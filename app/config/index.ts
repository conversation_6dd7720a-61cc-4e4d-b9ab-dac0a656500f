import { createConfig } from '../utils/config'
import { aggregator } from './aggregator'
import { buy } from './buy'
import { cleaner } from './cleaner'
import { computeUnits } from './compute-units'
import { database } from './database'
import { filter } from './filter'
import { geyser } from './geyser'
import { indexer } from './indexer'
import { logger } from './logger'
import { notification } from './notification'
import { rpcClient } from './rpc-client'
import { rpcServer } from './rpc-server'
import { sell } from './sell'
import { senders } from './senders'
import { tracker } from './tracker'
import { wallet } from './wallet'

export const config = createConfig({
    logger,
    database,
    rpcClient,
    rpcServer,
    geyser,
    wallet,
    senders,
    computeUnits,
    indexer,
    aggregator,
    filter,
    cleaner,
    buy,
    sell,
    tracker,
    notification,
})
