import type { TokenAccount, WalletDatasource } from '../modules/wallet/types'
import { highlight, message } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { createDeferred } from '@kdt310722/utils/promise'
import { shorten } from '@kdt310722/utils/string'
import { type Address, createKeyPairSignerFromBytes } from '@solana/kit'
import base58 from 'bs58'
import { config } from '../config'
import { Wallet } from '../modules/wallet/wallet'
import { formatPumpfunToken } from '../utils/formatters/format-pumpfun-token'
import { formatSol } from '../utils/formatters/format-sol'
import { GeyserWalletDatasource } from '../utils/geyser-client/wallet-datasource'
import { RpcWalletDatasource } from '../utils/rpc-websocket/wallet-datasource'
import { geyser, isGeyserEnabled } from './geyser'
import { createChildLogger } from './logger'
import { rpcWebsocketClient } from './rpc-websocket-client'

const logger = createChildLogger('core:wallet')

export function getDatasources(wallet: Address) {
    const datasources: WalletDatasource[] = [new RpcWalletDatasource(rpcWebsocketClient, wallet)]

    if (isGeyserEnabled()) {
        datasources.push(new GeyserWalletDatasource(geyser, wallet))
    }

    return datasources
}

let wallet: Wallet | undefined

export function getWallet() {
    return wallet!
}

let reloading: Promise<void> | undefined

export async function reloadWallet() {
    const reload = async () => {
        const timer = tap(logger.createTimer(), () => logger.info('Reloading wallet...'))

        try {
            await getWallet().init().then(() => logger.stopTimer(timer, 'info', 'Wallet reloaded!')).catch((error) => {
                logger.forceExit(1, 'fatal', 'Failed to reload wallet', error)
            })
        } finally {
            reloading = undefined
        }
    }

    return reloading ??= reload()
}

export function registerWalletEvents(wallet: Wallet) {
    wallet.on('balance', (balance) => logger.info(`Current wallet balance: ${highlight(formatSol(balance))}`))

    wallet.tokenAccount.on('add', ({ mint, amount }) => logger.debug(message(() => `New token account: ${highlight(`mint=${mint}`)}, ${highlight(`amount=${formatPumpfunToken(amount)}`)}`)))
    wallet.tokenAccount.on('update', ({ mint, amount }) => logger.debug(message(() => `Token account updated: ${highlight(`mint=${mint}`)}, ${highlight(`amount=${formatPumpfunToken(amount)}`)}`)))
    wallet.tokenAccount.on('remove', ({ mint }) => logger.debug(message(() => `Token account removed: ${highlight(`mint=${mint}`)}`)))
    wallet.tokenAccount.on('error', (error) => logger.exit(1, 'fatal', error))

    wallet.trade.on('trade', (_, { mint, isBuy, solAmount, tokenAmount }) => {
        logger.debug(message(() => `New Trade: ${highlight(`mint=${mint}`)}, ${highlight(`type=${isBuy ? 'buy' : 'sell'}`)}, ${highlight(`sol=${formatSol(solAmount)}`)}, ${highlight(`token=${formatPumpfunToken(tokenAmount)}`)}`))
    })

    return wallet
}

export async function initializeWallet() {
    const stop = logger.createLoading().start('Initialize wallet...')
    const tokenAccounts = createDeferred<TokenAccount[]>()
    const signer = await createKeyPairSignerFromBytes(base58.decode(config.wallet.privateKey))
    const datasources = getDatasources(signer.address)

    for (const datasource of datasources) {
        await datasource.subscribe()
    }

    wallet = registerWalletEvents(new Wallet(datasources, signer))

    wallet.tokenAccount.once('load', (accounts) => {
        tokenAccounts.resolve(accounts)
    })

    await wallet.init().then(() => tokenAccounts).then((accounts) => {
        stop(`Wallet: ${highlight(shorten(signer.address, 4))}, Balance: ${highlight(formatSol(wallet!.balance))}, Token Accounts: ${highlight(format(accounts.length))}`)
    })
}
