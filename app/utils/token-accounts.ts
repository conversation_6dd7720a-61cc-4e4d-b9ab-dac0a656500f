import type { SubscribeRequestFilterAccountsFilter } from '@kdt-sol/geyser-client'
import base58 from 'bs58'

export const getTokenAccountFilters = (wallet: string) => [
    { dataSize: 165 },
    { memcmp: { encoding: 'base58', offset: 32, bytes: wallet } },
]

export const getTokenAccountGeyserFilters = (wallet: string): SubscribeRequestFilterAccountsFilter[] => [
    { datasize: '165' },
    { memcmp: { offset: '32', bytes: base58.decode(wallet) } },
]
