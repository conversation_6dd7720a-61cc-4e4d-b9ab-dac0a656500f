import { isString, trim } from '@kdt310722/utils/string'
import { z } from 'zod'
import { bool } from '../utils/schemas/bool'
import { nullish } from '../utils/schemas/nullish'

const levels = ['query', 'schema', 'error', 'warn', 'info', 'log', 'migration'] as const
const logging = z.preprocess((value) => (isString(value) ? (value === 'all' ? levels : value.split(',').map((i) => trim(i))) : value), z.enum(levels).array())

export const database = z.object({
    host: z.string().default('localhost'),
    port: z.coerce.number().int().positive().default(5432),
    username: z.string().default('postgres'),
    password: z.string().default('postgres'),
    name: z.string(),
    schema: z.string().default('public'),
    logging: logging.default(['error', 'warn', 'migration']),
    maxQueryExecutionTime: z.coerce.number().int().positive().default(1000),
    dropSchema: bool.default(false),
    runMigrations: bool.default(true),
    poolSize: nullish(z.coerce.number().int().positive()),
})
