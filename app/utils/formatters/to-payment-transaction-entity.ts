import type { Token } from '../../modules/indexer/types/entities/token'
import type { DexScreenerPaymentTransaction } from '../../types/tracker'
import { tap } from '@kdt310722/utils/function'
import { PaymentTransaction } from '../../entities/payment-transaction'

export const toPaymentTransactionEntity = (data: DexScreenerPaymentTransaction, tokens: Token[]): PaymentTransaction => tap(new PaymentTransaction(), (entity) => {
    entity.slot = data.slot
    entity.signature = data.signature
    entity.payer = data.payer
    entity.recipient = data.recipient
    entity.amount = data.amount
    entity.datasource = data.datasource
    entity.receivedAt = new Date(data.receivedAt)
    entity.blockTime = data.blockTime
    entity.nodeTime = data.nodeTime ? new Date(data.nodeTime) : undefined
    entity.tokens = tokens
})
