import type { TokenStats } from '../../../indexer/types/entities/token-stats'
import type { Trade } from '../../../indexer/types/entities/trade'

export const toTokenStats = (trade: Trade): TokenStats => ({
    mint: trade.mint,
    sortKey: trade.sortKey,
    signature: trade.signature,
    slot: trade.slot,
    transactionIndex: trade.transactionIndex,
    eventIndex: trade.eventIndex,
    atl: trade.tokenPrice,
    ath: trade.tokenPrice,
    buyVolume: trade.isBuy ? trade.solAmount : 0n,
    sellVolume: trade.isBuy ? 0n : trade.solAmount,
    buysCount: trade.isBuy ? 1 : 0,
    sellsCount: trade.isBuy ? 0 : 1,
    tradersCount: 1,
    buyersCount: trade.isBuy ? 1 : 0,
    sellersCount: trade.isBuy ? 0 : 1,
    lastTrade: trade.timestamp,
})
