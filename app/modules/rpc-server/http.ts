import type { Logger } from '@kdt310722/logger'
import type { AnyObject } from '@kdt310722/utils/object'
import { RpcMessageHandler, type RpcMethodHandler } from '@kdt310722/rpc'
import { stringifyJson } from '@kdt310722/utils/json'
import cors from 'cors'
import createExpress, { type Express, type Request, type Response, text } from 'express'
import helmet from 'helmet'
import { createChildLogger } from '../../core/logger'
import { handleRequestError } from './utils/errors'

export interface HttpRpcServerOptions<TContext extends AnyObject = AnyObject> {
    context?: TContext
    corsOrigins?: string | string[]
    methods?: Record<string, RpcMethodHandler<TContext>>
    maxBatchSize?: number
    operationTimeout?: number
}

export class HttpRpcServer<TContext extends AnyObject = AnyObject> {
    public readonly server: Express

    protected readonly logger: Logger
    protected readonly handler: RpcMessageHandler
    protected readonly context: TContext

    public constructor({ context, maxBatchSize, operationTimeout, ...options }: HttpRpcServerOptions<TContext> = {}) {
        this.logger = createChildLogger('rpc-server:http')
        this.context = context ?? {} as TContext
        this.server = this.createServer(options)
        this.handler = new RpcMessageHandler({}, { operationTimeout, maxBatchSize, errorHandler: this.handleError.bind(this) })
    }

    public addMethod(name: string, method: RpcMethodHandler<TContext>, overwrite = false) {
        this.handler.addMethod(name, method, overwrite)
    }

    protected async handleRequest(request: Request, response: Response) {
        response.status(200).contentType('application/json').send(stringifyJson(await this.handler.handleMessage(request.body, this.context)))
    }

    protected handleError(error: unknown, request?: Request) {
        return handleRequestError(this.logger, error, request)
    }

    protected handleFallback(_: Request, response: Response) {
        response.sendStatus(404)
    }

    protected createServer({ corsOrigins = '*' }: HttpRpcServerOptions = {}) {
        const server = createExpress()

        server.use(helmet({ hidePoweredBy: true }))
        server.use(cors({ origin: corsOrigins }))
        server.use(text({ type: 'application/json' }))

        server.post('/', this.handleRequest.bind(this))
        server.all('*', this.handleFallback.bind(this))

        return server
    }
}
