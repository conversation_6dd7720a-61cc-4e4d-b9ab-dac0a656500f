import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCreatedAtColumnToRequestStatsTable1745586046669 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_stats" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "request_stats" DROP COLUMN "created_at"`)
    }
}
