import type { Nullable } from '@kdt310722/utils/common'
import { isInMode } from '@kdt310722/utils/node'
import { type Awaitable, tap } from '@kdt310722/utils/promise'
import { DataSource } from 'typeorm'
import { config } from '../config'
import { DatabaseLogger } from '../utils/database/logger'
import { NamingStrategy } from '../utils/database/naming-strategy'
import { appPath, rootPath } from '../utils/path'
import { createChildLogger } from './logger'

export const databaseLogger = createChildLogger('core:database')

export const database = new DataSource({
    ...config.database,
    type: 'postgres',
    database: config.database.name,
    entities: [appPath('entities', isInMode('build') ? '*.js' : '*.ts')],
    migrations: isInMode('build') ? [] : [rootPath('migrations', '*.ts')],
    migrationsRun: config.database.runMigrations,
    logger: new DatabaseLogger(databaseLogger, config.database.logging),
    namingStrategy: new NamingStrategy(),
})

export function setMaxQueryExecutionTime(time: Nullable<number>) {
    return database.setOptions({ maxQueryExecutionTime: time ?? undefined })
}

export async function skipSlowQueryLogging<T>(fn: () => Awaitable<T>) {
    return Promise.resolve(setMaxQueryExecutionTime(null)).then(fn).then(tap(() => setMaxQueryExecutionTime(config.database.maxQueryExecutionTime)))
}

export async function initializeDatabase() {
    const complete = databaseLogger.createLoading().start('Initializing database...')

    return skipSlowQueryLogging(() => database.initialize()).then(() => {
        complete('Database is initialized!')
    })
}
