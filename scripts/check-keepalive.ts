#!/usr/bin/env tsx

import { performance } from 'node:perf_hooks'
import { Pool } from 'undici'

interface KeepAliveResult {
    url: string
    supportsKeepAlive: boolean
    connectionReuseCount: number
    averageResponseTime: number
    keepAliveTimeout?: number
    totalRequests: number
    errors: string[]
    connectionStats: {
        newConnections: number
        reusedConnections: number
    }
}

interface RequestTiming {
    start: number
    end: number
    duration: number
    connectionReused: boolean
}

class KeepAliveChecker {
    private pool: Pool | null = null
    private readonly timings: RequestTiming[] = []
    private readonly errors: string[] = []
    private connectionCount = 0
    private reuseCount = 0

    constructor(private readonly url: string, private readonly options: {
        maxRequests?: number
        requestInterval?: number
        timeout?: number
        maxConnections?: number
    } = {}) {
        this.options = {
            maxRequests: 10,
            requestInterval: 100,
            timeout: 30_000,
            maxConnections: 1,
            ...options,
        }
    }

    async checkKeepAlive(): Promise<KeepAliveResult> {
        console.log(`🔍 Checking Keep-Alive support for: ${this.url}`)
        console.log(`📊 Configuration:`)
        console.log(`   - Max requests: ${this.options.maxRequests}`)
        console.log(`   - Request interval: ${this.options.requestInterval}ms`)
        console.log(`   - Timeout: ${this.options.timeout}ms`)
        console.log(`   - Max connections: ${this.options.maxConnections}`)
        console.log('')

        try {
            // Parse URL to get the origin for the pool
            const parsedUrl = new URL(this.url)
            const origin = `${parsedUrl.protocol}//${parsedUrl.host}`

            // Create pool with specific configuration for keep-alive testing
            this.pool = new Pool(origin, {
                connections: this.options.maxConnections,
                keepAliveTimeout: this.options.timeout,
                keepAliveMaxTimeout: this.options.timeout,
                pipelining: 1,
            })

            // Listen to pool events to track connections
            this.pool.on('connect', () => {
                this.connectionCount++
                console.log(`🔗 New connection established (total: ${this.connectionCount})`)
            })

            this.pool.on('disconnect', () => {
                console.log(`❌ Connection disconnected`)
            })

            // Perform multiple requests
            await this.performRequests()

            // Analyze results
            return this.analyzeResults()
        } catch (error) {
            this.errors.push(`Failed to check keep-alive: ${error}`)
            throw error
        } finally {
            await this.cleanup()
        }
    }

    private async performRequests(): Promise<void> {
        console.log(`🚀 Starting ${this.options.maxRequests} requests...`)

        const parsedUrl = new URL(this.url)
        const fullPath = parsedUrl.pathname + parsedUrl.search + parsedUrl.hash

        for (let i = 0; i < this.options.maxRequests!; i++) {
            try {
                const start = performance.now()

                const response = await this.pool!.request({
                    path: fullPath || '/',
                    method: 'GET',
                    headers: {
                        'Connection': 'keep-alive',
                        'User-Agent': 'KeepAlive-Checker/1.0',
                    },
                })

                const end = performance.now()
                const duration = end - start

                // Consume response body to complete the request
                await response.body.text()

                const timing: RequestTiming = {
                    start,
                    end,
                    duration,
                    connectionReused: i > 0 && this.connectionCount === 1,
                }

                this.timings.push(timing)

                if (timing.connectionReused) {
                    this.reuseCount++
                }

                console.log(`📈 Request ${i + 1}: ${duration.toFixed(2)}ms (${timing.connectionReused ? 'reused' : 'new'} connection)`)

                // Wait between requests to test keep-alive
                if (i < this.options.maxRequests! - 1) {
                    await this.sleep(this.options.requestInterval!)
                }
            } catch (error) {
                const errorMsg = `Request ${i + 1} failed: ${error}`
                this.errors.push(errorMsg)
                console.error(`❌ ${errorMsg}`)
            }
        }
    }

    private analyzeResults(): KeepAliveResult {
        const successfulRequests = this.timings.length

        const averageResponseTime = successfulRequests > 0 ? this.timings.reduce((sum, t) => sum + t.duration, 0) / successfulRequests : 0

        // Determine if keep-alive is supported
        const supportsKeepAlive = this.reuseCount > 0 || this.connectionCount === 1

        // Estimate keep-alive timeout based on connection behavior
        let keepAliveTimeout: number | undefined

        if (supportsKeepAlive && this.timings.length > 1) {
            // If connections were reused, estimate timeout
            const maxGapBetweenRequests = Math.max(...this.timings.slice(1).map((t, i) => t.start - this.timings[i].end))

            keepAliveTimeout = Math.floor(maxGapBetweenRequests + this.options.requestInterval!)
        }

        const result: KeepAliveResult = {
            url: this.url,
            supportsKeepAlive,
            connectionReuseCount: this.reuseCount,
            averageResponseTime,
            keepAliveTimeout,
            totalRequests: successfulRequests,
            errors: this.errors,
            connectionStats: {
                newConnections: this.connectionCount,
                reusedConnections: this.reuseCount,
            },
        }

        this.printResults(result)

        return result
    }

    private printResults(result: KeepAliveResult): void {
        console.log(`\n${'='.repeat(60)}`)
        console.log('📋 KEEP-ALIVE ANALYSIS RESULTS')
        console.log('='.repeat(60))

        console.log(`🌐 URL: ${result.url}`)
        console.log(`✅ Keep-Alive Support: ${result.supportsKeepAlive ? 'YES' : 'NO'}`)

        if (result.supportsKeepAlive) {
            console.log(`🔄 Connection Reuse Count: ${result.connectionReuseCount}`)
            console.log(`🔗 New Connections: ${result.connectionStats.newConnections}`)
            console.log(`♻️  Reused Connections: ${result.connectionStats.reusedConnections}`)

            if (result.keepAliveTimeout) {
                console.log(`⏱️  Estimated Keep-Alive Timeout: ~${result.keepAliveTimeout}ms`)
            }
        }

        console.log(`📊 Total Successful Requests: ${result.totalRequests}`)
        console.log(`⚡ Average Response Time: ${result.averageResponseTime.toFixed(2)}ms`)

        if (result.errors.length > 0) {
            console.log(`❌ Errors (${result.errors.length}):`)
            result.errors.forEach((error) => console.log(`   - ${error}`))
        }

        // Performance analysis
        if (result.totalRequests > 1) {
            const firstRequestTime = this.timings[0]?.duration || 0
            const subsequentAvg = this.timings.slice(1).reduce((sum, t) => sum + t.duration, 0) / (this.timings.length - 1)

            console.log('\n📈 PERFORMANCE ANALYSIS:')
            console.log(`   First request: ${firstRequestTime.toFixed(2)}ms`)
            console.log(`   Subsequent avg: ${subsequentAvg.toFixed(2)}ms`)

            if (result.supportsKeepAlive && subsequentAvg < firstRequestTime) {
                console.log(`   🚀 Performance improvement: ${((firstRequestTime - subsequentAvg) / firstRequestTime * 100).toFixed(1)}%`)
            }
        }

        console.log('='.repeat(60))
    }

    private async cleanup(): Promise<void> {
        if (this.pool) {
            await this.pool.close()
            this.pool = null
        }
    }

    private sleep(ms: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, ms))
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2)

    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
        console.log(`
🔍 HTTP Keep-Alive Checker

Usage: tsx check-keepalive.ts <URL> [options]

Arguments:
  URL                    The URL to test for keep-alive support

Options:
  --requests <number>    Number of requests to make (default: 10)
  --interval <number>    Interval between requests in ms (default: 100)
  --timeout <number>     Connection timeout in ms (default: 30000)
  --connections <number> Max connections in pool (default: 1)
  --help, -h            Show this help message

Examples:
  tsx check-keepalive.ts https://httpbin.org/get
  tsx check-keepalive.ts https://example.com --requests 20 --interval 200
  tsx check-keepalive.ts http://localhost:3000 --timeout 10000
        `)

        process.exit(0)
    }

    const url = args[0]
    const options: any = {}

    // Parse command line options
    for (let i = 1; i < args.length; i += 2) {
        const flag = args[i]
        const value = args[i + 1]

        switch (flag) {
            case '--requests':
                options.maxRequests = Number.parseInt(value, 10)
                break
            case '--interval':
                options.requestInterval = Number.parseInt(value, 10)
                break
            case '--timeout':
                options.timeout = Number.parseInt(value, 10)
                break
            case '--connections':
                options.maxConnections = Number.parseInt(value, 10)
                break
        }
    }

    try {
        const checker = new KeepAliveChecker(url, options)
        await checker.checkKeepAlive()
        process.exit(0)
    } catch (error) {
        console.error(`💥 Fatal error: ${error}`)
        process.exit(1)
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error)
}

export { KeepAliveChecker, type KeepAliveResult }
