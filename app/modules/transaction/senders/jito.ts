import type { UrlLike } from '@kdt310722/rpc'
import type { SenderOptions } from './sender'
import { JITO_ENDPOINTS, JITO_MINIMUM_TIP_AMOUNT, JITO_TIP_ACCOUNTS } from '../constants'
import { Rpc<PERSON><PERSON> } from './rpc'

export interface JitoSenderOptions<TEndpoints extends Record<string, UrlLike> = typeof JITO_ENDPOINTS> extends SenderOptions {
    endpoints?: TEndpoints
}

export class JitoSender<TEndpoints extends Record<string, UrlLike> = typeof JITO_ENDPOINTS> extends RpcSender {
    public override readonly name = 'Jito'
    public override readonly tipWallets = JITO_TIP_ACCOUNTS
    public override readonly minimumTipAmount = JITO_MINIMUM_TIP_AMOUNT

    public constructor(endpoint: Extract<keyof TEndpoints, string>, { endpoints, ...options }: JitoSenderOptions<TEndpoints> = {}) {
        super(endpoints ?? JITO_ENDPOINTS, endpoint, options)
    }
}
