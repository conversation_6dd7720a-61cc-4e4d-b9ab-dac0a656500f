import type { AnyObject } from '@kdt310722/utils/object'
import type { BondingCurveAccount } from '../indexer/types/entities/bonding-curve'
import type { Token } from '../indexer/types/entities/token'
import type { TokenStats } from '../indexer/types/entities/token-stats'

export type AggregatedBondingCurve = Pick<BondingCurveAccount, 'sortKey' | 'slot' | 'virtualSolReserves' | 'virtualTokenReserves' | 'realSolReserves' | 'realTokenReserves' | 'creator' | 'feeBasisPoints' | 'creatorFeeBasisPoints'>

export interface AggregatedTokenStats extends Omit<TokenStats, 'mint' | 'tradersCount' | 'buyersCount' | 'sellersCount'> {
    price: bigint
    upFromAtl: number
    downFromAth: number
}

export interface Aggregated {
    token: Token
    metadata: AnyObject
    bondingCurve?: AggregatedBondingCurve
    stats?: AggregatedTokenStats
}
