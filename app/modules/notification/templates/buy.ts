import type { BuyFailedTransaction } from '../../../entities/buy-failed-transaction'
import type { DexScreenerPaymentTransaction } from '../../../types/tracker'
import { escapeMarkdownV2 } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { BigIntMath, formatNanoseconds, humanizeNumber } from '@kdt310722/utils/number'
import { formatDate } from '@kdt310722/utils/time'
import { calculateVirtualReservesBefore } from '@kdt-sol/pumpfun-sdk'
import { getMarketCap } from '../../../common/indexer'
import { BuyTransaction } from '../../../entities/buy-transaction'
import { formatPumpfunToken } from '../../../utils/formatters/format-pumpfun-token'
import { formatSol } from '../../../utils/formatters/format-sol'
import { tokenLink, txLink } from '../utils/messages'
import { getTokenHelpLinks } from './transaction'

export interface BuyNotificationData {
    buyAt: Date
    transaction: BuyTransaction | BuyFailedTransaction
    isUsingCached: boolean
    bondingCurveSlot: number
    paymentTransaction?: DexScreenerPaymentTransaction
    processTimeUntilSend?: number
}

export function getBuyNotificationMessage({ buyAt, transaction, isUsingCached, bondingCurveSlot, paymentTransaction, processTimeUntilSend }: BuyNotificationData, name?: string) {
    const message: string[] = []
    const token = transaction.tokenInfo

    if (transaction instanceof BuyTransaction) {
        message.push(`🚀 *Buy Alert* 🚀`)
    } else {
        message.push(`❌ *Buy Failed* ❌`)
    }

    message.push('')

    if (notNullish(name)) {
        message.push(` \\- Name: *${name}*`)
    }

    message.push(
        ` \\- Token: *${tokenLink(token.mint, token.name)}*`,
        ` \\- Time: *${escapeMarkdownV2(formatDate(buyAt, true))}*`,
        ` \\- Amount: *${escapeMarkdownV2(formatSol(transaction.amount))}*`,
        ` \\- Is Using Cached: *${isUsingCached ? 'Yes' : 'No'}*`,
    )

    if (notNullish(processTimeUntilSend)) {
        message.push(` \\- Total Process Time: *${Math.max(processTimeUntilSend, 0)}ms*`)
    }

    message.push(` \\- Bonding Curve Slot: *${bondingCurveSlot}*`)

    if (notNullish(paymentTransaction)) {
        message.push(` \\- Track Slot: *${paymentTransaction.slot}*`)
    }

    if (transaction instanceof BuyTransaction) {
        const bondingCurveBefore = calculateVirtualReservesBefore({ ...transaction.bondingCurve, isBuy: true, solAmount: transaction.solCost, tokenAmount: transaction.receivedTokens })
        const marketCapBefore = getMarketCap(bondingCurveBefore)
        const marketCapAfter = getMarketCap(transaction.bondingCurve)

        message.push(
            ` \\- Slot: *${transaction.slot}*`,
            ` \\- Buy At MCap: *${escapeMarkdownV2(humanizeNumber(marketCapBefore))}*`,
            ` \\- MCap After Buy: *${escapeMarkdownV2(humanizeNumber(marketCapAfter))}*`,
            ` \\- Sender: *${transaction.sender}*`,
            ` \\- Cost: *${escapeMarkdownV2(formatSol(BigIntMath.abs(transaction.balanceAfter - transaction.balanceBefore)))}*`,
            ` \\- Bought Amount: *${escapeMarkdownV2(formatSol(transaction.solCost))}*`,
            ` \\- Received Amount: *${escapeMarkdownV2(formatPumpfunToken(transaction.receivedTokens))} ${escapeMarkdownV2(token.symbol)}*`,
            ` \\- Process Time: *${formatNanoseconds(transaction.processTime)}*`,
            ` \\- Send Time: *${formatNanoseconds(transaction.sendTime)}*`,
            ` \\- Confirmation Time: *${formatNanoseconds(BigInt(transaction.confirmationTime * 1e6))}*`,
            '',
            `${txLink(transaction.signature, 'TX')} \\| ${getTokenHelpLinks(transaction.mint)}`,
        )
    } else {
        message.push(` \\- Process Time: *${formatNanoseconds(transaction.processTime)}*`)

        const formatedErrors = Object.fromEntries(transaction.items.map((item, index) => [index, escapeMarkdownV2(JSON.stringify(item.err, null, 2))]))
        const isAllSameError = Object.values(formatedErrors).every((i) => i === formatedErrors[0])

        for (const [i, item] of transaction.items.entries()) {
            message.push(
                '',
                ` \\- *${item.sender}*:`,
                `    \\+ Slot: *${item.slot}*`,
                `    \\+ TX: *${txLink(item.signature)}*`,
                `    \\+ Send Time: *${formatNanoseconds(item.sendTime)}*`,
                `    \\+ Confirmation Time: *${formatNanoseconds(BigInt(item.confirmationTime * 1e6))}*`,
            )

            if (!isAllSameError) {
                message.push(`\`\`\`json\n${formatedErrors[i]}\`\`\``)
            }
        }

        if (isAllSameError) {
            message.push('', `\`\`\`json\n${formatedErrors[0]}\`\`\``)
        }

        message.push('', getTokenHelpLinks(transaction.mint))
    }

    return message.join('\n')
}
