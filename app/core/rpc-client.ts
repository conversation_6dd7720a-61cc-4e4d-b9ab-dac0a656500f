import { context } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { createSolanaRpcFromTransport } from '@solana/kit'
import { config } from '../config'
import { createSolanaRpcTransport } from '../utils/rpc-client/transports'
import { toTransportResponse } from '../utils/rpc-client/utils'
import { createChildLogger } from './logger'

const logger = createChildLogger('core:clients:rpc')
const requestLogger = logger.child({ name: 'request' })
const responseLogger = logger.child({ name: 'response' })
const httpConfig = config.rpcClient.http

const transport = createSolanaRpcTransport({
    ...httpConfig,
    onRequest: (request) => tap(responseLogger.createTimer(`rpc-client:request:${request.id}`), () => requestLogger.debug('Rpc request', request)),
    onResponse: (body, response, request) => responseLogger.stopTimer(`rpc-client:request:${request.id}`, 'debug', 'Rpc response', context(() => [{ request: request.id, response: toTransportResponse(body, response) }])),
    retry: { ...httpConfig.retry, onFailedAttempt: (error) => logger.warn('Request failed, retrying...', error) },
})

export const rpcClient = createSolanaRpcFromTransport(transport)

export async function initializeRpcClient() {
    const stop = logger.createLoading().start('Checking RPC client connection...')

    await rpcClient.getHealth().send().then(() => {
        stop('RPC client connection is healthy!')
    })
}
