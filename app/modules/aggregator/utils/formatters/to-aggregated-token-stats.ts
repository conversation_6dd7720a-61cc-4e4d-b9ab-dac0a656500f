import type { TokenStats } from '../../../indexer/types/entities/token-stats'
import type { AggregatedBondingCurve, AggregatedTokenStats } from '../../types'
import { omit } from '@kdt310722/utils/object'
import { calculatePercentDrop, calculatePercentIncrease } from '../../../../utils/prices'
import { getPumpFunTokenPrice } from '../../../../utils/pumpfun'

export const toAggregatedTokenStats = (stats: TokenStats, bondingCurve: AggregatedBondingCurve): AggregatedTokenStats => {
    const price = getPumpFunTokenPrice(bondingCurve)
    const upFromAtl = calculatePercentIncrease(stats.atl, price)
    const downFromAth = calculatePercentDrop(stats.ath, price)

    return { ...omit(stats, 'mint', 'tradersCount', 'buyersCount', 'sellersCount'), price, upFromAtl, downFromAth }
}
