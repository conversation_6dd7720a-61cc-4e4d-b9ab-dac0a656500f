import { <PERSON>umn, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn } from 'typeorm'
import { BigIntColumn } from '../utils/database/columns/bigint-column'

@Entity({ name: 'request_stats' })
export class RequestStats {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Column('varchar')
    public declare sender: string

    @Column('varchar')
    public declare endpoint: string

    @Column()
    public declare requestId: number

    @BigIntColumn()
    public declare buildTime: bigint

    @BigIntColumn()
    public declare sendTime: bigint

    @BigIntColumn()
    public declare parseResponseTime: bigint

    @CreateDateColumn()
    public declare createdAt: Date
}
