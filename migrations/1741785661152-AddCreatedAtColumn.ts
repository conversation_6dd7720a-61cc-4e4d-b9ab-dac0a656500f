import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCreatedAtColumn1741785661152 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "buy_failed_transactions" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`)
        await queryRunner.query(`ALTER TABLE "buy_transactions" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "buy_transactions" DROP COLUMN "created_at"`)
        await queryRunner.query(`ALTER TABLE "buy_failed_transactions" DROP COLUMN "created_at"`)
    }
}
