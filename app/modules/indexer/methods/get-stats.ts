import type { Address } from '@solana/kit'
import type { IndexerClient } from '../indexer-client'
import type { TokenStatsApiResponse } from '../types/entities/token-stats'
import { notNullish } from '@kdt310722/utils/common'
import { formatTokenStats } from '../utils/formatters'

export interface GetStatsOptions {
    startSlot?: number
    endSlot?: number
    startSortKey?: bigint
    endSortKey?: bigint
}

export async function getStats(client: IndexerClient, mints: Array<Address | string>, options: GetStatsOptions = {}) {
    return client.call<Array<TokenStatsApiResponse | null>>('getMultipleTokenStats', [mints, options]).then((result) => result.map((stats) => (notNullish(stats) ? formatTokenStats(stats) : null)))
}
