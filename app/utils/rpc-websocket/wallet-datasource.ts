import type { WalletDatasource, WalletDatasourceEvents } from '../../modules/wallet/types'
import type { SolanaRpcWebsocketClient } from './solana-rpc-websocket-client'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { TOKEN_PROGRAM_ADDRESS } from '@solana-program/token'
import { type Account, type Address, address, lamports } from '@solana/kit'
import { getTransaction, type GetTransactionResponse } from '../rpc-client/transactions'
import { getTokenAccountFilters } from '../token-accounts'
import { toWalletDatasourceTransaction } from './transactions'

export class RpcWalletDatasource extends Emitter<WalletDatasourceEvents, true> implements WalletDatasource {
    public constructor(protected readonly client: SolanaRpcWebsocketClient, protected readonly wallet: Address) {
        super()
    }

    public async subscribe() {
        await this.subscribeAccount()
        await this.subscribeTokenAccount()
        await this.subscribeTransaction()
    }

    public async subscribeAccount() {
        await this.client.subscribe('accountSubscribe', [this.wallet, { encoding: 'base64', commitment: 'confirmed' }], 'accountUnsubscribe', this.handleAccount.bind(this))
    }

    public async subscribeTokenAccount() {
        await this.client.subscribe('programSubscribe', [TOKEN_PROGRAM_ADDRESS, { encoding: 'base64', commitment: 'confirmed', filters: getTokenAccountFilters(this.wallet) }], 'programUnsubscribe', this.handleTokenAccount.bind(this))
    }

    public async subscribeTransaction() {
        await this.client.subscribe('logsSubscribe', [{ mentions: [this.wallet] }, { commitment: 'confirmed' }], 'logsUnsubscribe', this.handleLogs.bind(this))
    }

    protected handleLogs(data: any) {
        if (notNullish(data.value.err)) {
            return
        }

        getTransaction(data.value.signature, 'confirmed', false).then((tx) => notNullish(tx) && this.handleTransaction(data.value.signature, tx)).catch((error) => {
            this.client.socket.emit('error', error)
        })
    }

    protected handleTransaction(signature_: string, tx: GetTransactionResponse) {
        this.emit('transaction', this.wallet, toWalletDatasourceTransaction(signature_, tx), { slot: Number(tx.slot) })
    }

    protected handleAccount(data: any) {
        this.emit('account', this.wallet, this.toSolanaAccount(this.wallet, data.value), { slot: data.context.slot })
    }

    protected handleTokenAccount(data: any) {
        this.emit('tokenAccount', this.wallet, this.toSolanaAccount(data.value.pubkey, data.value.account), { slot: data.context.slot })
    }

    protected toSolanaAccount(pubkey: string, data: any): Account<Uint8Array> {
        return { address: address(pubkey), programAddress: address(data.owner), lamports: lamports(BigInt(data.lamports)), executable: data.executable, data: Buffer.from(data.data[0], 'base64'), space: 0n }
    }
}
