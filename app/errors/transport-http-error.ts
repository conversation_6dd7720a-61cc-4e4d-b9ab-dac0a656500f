import type { TransportRequest, TransportResponse } from '../utils/rpc-client/types'
import { toTransportResponse } from '../utils/rpc-client/utils'
import { BaseError } from './base-error'

export class TransportHttpError extends BaseError {
    public declare readonly request?: TransportRequest
    public declare readonly response?: TransportResponse

    public withRequest(request?: TransportRequest) {
        return this.withValue('request', request)
    }

    public withResponse(body: string, response: Response) {
        return this.withValue('response', toTransportResponse(body, response))
    }
}
