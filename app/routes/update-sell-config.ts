import type { z } from 'zod'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { config } from '../config'
import { schema as sell } from '../config/sell'

export const schema = sell.partial()

export const handler = (params: z.infer<typeof schema>) => {
    if (isNullish(config.sell)) {
        throw new Error('Sell not enabled')
    }

    for (const [key, value] of Object.entries(params)) {
        if (notNullish(value)) {
            config.sell[key] = value
        }
    }

    return config.sell
}
