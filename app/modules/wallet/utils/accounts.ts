import type { Address, Commitment, IAccountMeta } from '@solana/kit'
import { transform } from '@kdt310722/utils/function'
import { isString } from '@kdt310722/utils/string'
import { identifyTokenInstruction, parseCloseAccountInstruction, TokenInstruction } from '@solana-program/token'
import base58 from 'bs58'
import { rpcClient } from '../../../core/rpc-client'
import { parseInstruction, type RawInstruction } from '../../../utils/rpc-client/instructions'

export const getAccountBalance = async (wallet: Address, commitment: Commitment = 'confirmed') => rpcClient.getBalance(wallet, { commitment }).send().then((r) => r.value)

export interface CloseTokenAccountInstruction {
    address: Address
    owner: Address
}

export function getCloseTokenAccountInstructions(accounts: IAccountMeta[], instructions: RawInstruction[]) {
    const tokenAccounts: CloseTokenAccountInstruction[] = []

    for (const instruction of instructions) {
        try {
            const data = isString(instruction.data) ? base58.decode(instruction.data) : instruction.data
            const type = identifyTokenInstruction({ data })

            if (type === TokenInstruction.CloseAccount) {
                tokenAccounts.push(transform(parseCloseAccountInstruction(parseInstruction(accounts, { ...instruction, data })), (i) => ({ address: i.accounts.account.address, owner: i.accounts.owner.address })))
            }
        } catch (error) {
            if (error instanceof Error && error.message === 'The provided instruction could not be identified as a token instruction.') {
                continue
            }

            throw error
        }
    }

    return tokenAccounts
}
