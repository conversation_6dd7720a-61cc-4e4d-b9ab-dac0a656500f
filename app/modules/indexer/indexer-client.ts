import type { NonEmptyArray } from 'zod-validation-error'
import type { GlobalAccount } from './types/entities/global-account'
import type { Token } from './types/entities/token'
import type { Trade } from './types/entities/trade'
import type { GetParams } from './types/params'
import { type RequestPayload, RpcWebSocketClient } from '@kdt310722/rpc'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/promise'
import { type Address, address } from '@solana/kit'
import { getBondingCurves } from './methods/get-bonding-curves'
import { getSolPrice } from './methods/get-sol-price'
import { getStats, type GetStatsOptions } from './methods/get-stats'
import { getTokens, getTokensCount } from './methods/get-tokens'
import { getTraders } from './methods/get-traders'
import { getTrades, type GetTradesParams } from './methods/get-trades'
import { formatToken, formatTrade } from './utils/formatters'

export type IndexerClientEvents = {
    slot: (slot: number) => void
    globalAccount: (account: GlobalAccount) => void
    token: (token: Token) => void
    gapToken: (token: Token) => void
    trade: (trade: Trade) => void
    gapTrade: (trade: Trade) => void
    solPrice: (price: number) => void
}

export type IndexerEvent = 'slot' | 'globalAccount' | 'token' | `token:${string}` | 'gapToken' | 'trade' | `trade:${string}` | 'gapTrade' | 'solPrice'

export class IndexerClient extends RpcWebSocketClient {
    public readonly notifications = new Emitter<IndexerClientEvents, true>()
    public readonly subscriptions = new Set<IndexerEvent>()

    public constructor(...args: ConstructorParameters<typeof RpcWebSocketClient>) {
        super(...args)

        this.on('notification', (method, params) => {
            this.handleNotification(method, params)
        })
    }

    public getTokens(params?: GetParams) {
        return getTokens(this, params)
    }

    public getTokensCount(params?: Omit<GetParams, 'limit'>) {
        return getTokensCount(this, params)
    }

    public async getTrades(params?: GetTradesParams) {
        return getTrades(this, params)
    }

    public async getTraders(mints: Array<Address | string>) {
        return getTraders(this, mints)
    }

    public async getStats(mints: Array<Address | string>, options?: GetStatsOptions) {
        return getStats(this, mints, options)
    }

    public async getBondingCurves(mints: Array<Address | string>) {
        return getBondingCurves(this, mints)
    }

    public async getSolPrice() {
        return getSolPrice(this)
    }

    public async resubscribe(beforeSubscribe?: (events: string[]) => void) {
        return Promise.resolve([...this.subscriptions]).then(tap((events) => beforeSubscribe?.(events))).then(tap(async (events) => this.subscribe(events)))
    }

    public async subscribe(events: IndexerEvent[]) {
        await this.batchCall(this.getSubscribePayload(events)).then(() => events.map((event) => this.subscriptions.add(event)))

        return async () => {
            for (const event of events) {
                this.subscriptions.delete(event)
            }

            await this.batchCall(this.getSubscribePayload(events, 'unsubscribe')).catch((error) => this.socket.emit('error', error))
        }
    }

    protected handleNotification(method: string, params: any) {
        if (method === 'slot') {
            return this.notifications.emit('slot', params)
        }

        if (method === 'globalAccount') {
            return this.notifications.emit('globalAccount', { ...params, feeRecipient: address(params.feeRecipient) })
        }

        if (method === 'token' || method.startsWith('token:')) {
            return this.notifications.emit('token', formatToken(params))
        }

        if (method === 'gapToken') {
            return this.notifications.emit('gapToken', formatToken(params))
        }

        if (method === 'trade' || method.startsWith('trade:')) {
            return this.notifications.emit('trade', formatTrade(params))
        }

        if (method === 'gapTrade') {
            return this.notifications.emit('gapTrade', formatTrade(params))
        }

        if (method === 'solPrice') {
            return this.notifications.emit('solPrice', params)
        }

        return this.emit('unhandledMessage', JSON.stringify({ method, params }))
    }

    protected getSubscribePayload(events: IndexerEvent[], method = 'subscribe') {
        return events.map((event) => this.createRequestPayload(method, event)) as NonEmptyArray<RequestPayload>
    }
}
