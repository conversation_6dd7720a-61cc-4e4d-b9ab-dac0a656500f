import type { ZodTypeAny } from 'zod'
import { defineConfig } from '@kdt310722/config'
import { printConfigError } from './cli'

export function createConfig<S extends Record<string, ZodTypeAny>>(schema: S, name = 'config') {
    const config = defineConfig(schema, { search: { name }, formatError: false })

    try {
        return config.parse()
    } catch (error) {
        return printConfigError(error)
    }
}
