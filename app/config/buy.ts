import { z } from 'zod'
import { solAmount } from '../utils/schemas/amounts'

export const swap = z.object({
    slippage: z.number().nonnegative(),
    priorityFee: solAmount,
    tip: solAmount,
    timeout: z.number().positive().default(10_000),
    antiMev: z.boolean().default(true),
})

export const buy = swap.extend({
    enabled: z.boolean().default(true),
    cache: z.boolean().default(true),
    amount: solAmount,
    filter: z.string().nonempty(),
    minimumWalletBalance: solAmount,
})
