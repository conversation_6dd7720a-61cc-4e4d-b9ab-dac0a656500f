import type { z } from 'zod'
import type { AggregatedBondingCurve, AggregatedTokenStats } from '../modules/aggregator/types'
import { readFile } from 'node:fs/promises'
import { join } from 'node:path'
import { highlight, message } from '@kdt310722/logger'
import { unique } from '@kdt310722/utils/array'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import fg from 'fast-glob'
import { config } from '../config'
import { filterSchema } from '../config/filter'
import { createChildLogger } from '../core/logger'
import { rootPath } from '../utils/path'
import { isInRange } from '../utils/ranges'
import { aggregator } from './aggregator'

type FilterEvents = {
    filter: (mint: string, current: string[], previous: string[]) => void
}

export const filters: Record<string, z.infer<typeof filterSchema>> = {}
export const filter = new Emitter<FilterEvents, true>()

export function formatValue<T extends number | bigint | Date>(value: T) {
    if (value instanceof Date) {
        return (Date.now() - value.getTime()) / 1000
    }

    return value
}

export function isMatch(stats: AggregatedTokenStats, filter: string) {
    return Object.keys(filters[filter]).every((column) => isInRange(formatValue(stats[column]), filters[filter][column]))
}

export function getMatchedFilters(stats: AggregatedTokenStats) {
    return Object.keys(filters).filter((name) => isMatch(stats, name))
}

const logger = createChildLogger('common:filter')

function setFilters(mint: string, filters: string[], prev: string[]) {
    aggregator.setMetadata(mint, 'filters', filters)
    aggregator.setMetadata(mint, 'updatedAt', Date.now())
    filter.emit('filter', mint, filters, prev)

    if (filters.join(',') !== prev.join(',')) {
        logger.debug(message(() => `Filters for token ${highlight(mint)} updated: curr=${highlight(JSON.stringify(filters))}, prev=${highlight(JSON.stringify(prev))}`))
    }
}

aggregator.on('stats', (mint, stats) => {
    const prev = aggregator.get(mint)?.metadata.filters ?? []
    const curr = getMatchedFilters(stats)

    setFilters(mint, curr, prev)
})

function addFilter(mint: string, filter_: string) {
    const curr = aggregator.get(mint)?.metadata.filters ?? []
    const filters = unique([...curr, filter_])

    setFilters(mint, filters, curr)
}

export function isExpired(createdAt: Date) {
    return Date.now() - createdAt.getTime() >= config.aggregator.maxTokenAge
}

export function isCompleted(bondingCurve: AggregatedBondingCurve) {
    return bondingCurve.realTokenReserves === 0n
}

aggregator.on('bondingCurve', (mint, bondingCurve) => {
    if (isCompleted(bondingCurve)) {
        addFilter(mint, 'completed')
    }
})

function recheck() {
    logger.debug('Finding tokens not updated for a long time...')

    for (const [mint, { token: { createdAt }, metadata, stats, bondingCurve }] of Object.entries(aggregator.items)) {
        if (isExpired(createdAt)) {
            addFilter(mint, 'expired')
            continue
        }

        if (isNullish(metadata.updatedAt) || Date.now() - metadata.updatedAt < config.filter.maxAge) {
            continue
        }

        logger.debug(message(() => `Re emitting aggregator events for token ${highlight(mint)}...`))

        if (notNullish(bondingCurve)) {
            aggregator.emit('bondingCurve', mint, bondingCurve)
        }

        if (notNullish(stats)) {
            aggregator.emit('stats', mint, stats)
        }
    }
}

export const filterPath = rootPath('filters')

export async function loadFilters() {
    const files = await fg(join(filterPath, '*.json'))

    for (const file of files) {
        const name = file.slice(filterPath.length + 1, -5)

        try {
            filters[name] = filterSchema.parse(JSON.parse(await readFile(file, 'utf8')))
        } catch (error) {
            throw new Error(`Failed to load filter [${name}]:`, { cause: error })
        }
    }
}

export async function initializeFilter() {
    const stop = logger.createLoading().start('Initializing filter module...')

    await loadFilters().then(() => setInterval(recheck, config.filter.recheckInterval)).then(() => {
        stop('Filter module is initialized!')
    })
}
