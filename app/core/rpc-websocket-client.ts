import { highlight, LogLevel } from '@kdt310722/logger'
import { join } from '@kdt310722/utils/buffer'
import { isEmpty } from '@kdt310722/utils/common'
import { transform } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { config } from '../config'
import { getResubscribeEventsMessage } from '../utils/cli'
import { SolanaRpcWebsocketClient, type Subscription } from '../utils/rpc-websocket/solana-rpc-websocket-client'
import { createChildLogger } from './logger'
import { rpcServer } from './rpc-server'
import { reloadWallet } from './wallet'

rpcServer.ws.addEvent('rpc:slot')
rpcServer.ws.addEvent('rpc:latency')

const logger = createChildLogger('core:clients:rpc-websocket')
const client = new SolanaRpcWebsocketClient(config.rpcClient.websocket.url, config.rpcClient.websocket)

let isInitialized = false

client.socket.on('connected', () => {
    if (isInitialized) {
        logger.info('Connected to RPC websocket server!')

        const handleSuccess = (subscriptions: Subscription[]) => {
            logger.info(`Resubscribed to ${highlight(subscriptions.length)} events on RPC websocket server!`)
            client.socket.resetRetryCount()
            Promise.resolve().then(() => reloadWallet())
        }

        client.resubscribe((subscriptions) => logger.info(`Resubscribing to ${highlight(subscriptions.length)} events on RPC websocket server...${getResubscribeEventsMessage(Object.fromEntries(subscriptions.map((i, idx) => [idx, i])))}`)).then(handleSuccess).catch(async (error) => {
            await Promise.resolve(logger.error('Failed to resubscribe to events on RPC websocket server', error)).then(() => client.socket.disconnect(false))
        })
    }
})

let slotTimer: NodeJS.Timeout | undefined

client.socket.on('disconnected', (code, reason, isExplicitly) => {
    clearTimeout(slotTimer)

    if (!client.socket.isReconnecting) {
        logger.log(isExplicitly ? LogLevel.INFO : LogLevel.WARN, `Disconnected from RPC websocket server: ${highlight(`${code} - ${transform(join(reason), (r) => (isEmpty(r) ? 'EMPTY_REASON' : r))}`)}`)
    }

    if (client.socket.isReconnectAttemptReached) {
        logger.exit(1, 'fatal', 'Failed to reconnect to RPC websocket server')
    }
})

function handleError(error: unknown) {
    Promise.resolve(logger.error('RPC websocket server error', error)).then(() => client.socket.disconnect(false))
}

function handleUnhandledMessage(message: unknown) {
    logger.warn('Received unhandled message from RPC websocket server', { message: Buffer.isBuffer(message) ? join(message) : message })
}

client.socket.on('reconnect', (attempts) => logger.info(`Reconnecting to RPC websocket server (attempts: ${highlight(attempts)})...`))
client.socket.on('error', handleError)

client.on('error', handleError)
client.on('unhandledMessage', handleUnhandledMessage)
client.on('unhandledRpcMessage', handleUnhandledMessage)

function checkHealth() {
    const timeout = config.rpcClient.websocket.maxTimeBetweenTwoSlots

    if (slotTimer) {
        clearTimeout(slotTimer)
    }

    slotTimer = setTimeout(() => client.socket.isConnected && Promise.resolve(logger.error(`No RPC slots received after ${highlight(format(Math.floor(timeout / 1000)))} seconds`)).then(() => client.socket.disconnect(false)), timeout)
}

client.onSlot((slot) => {
    rpcServer.ws.emit('rpc:slot', slot)
    checkHealth()
})

client.onLatency((latency) => {
    rpcServer.ws.emit('rpc:latency', latency)
})

export const rpcWebsocketClient = client

export async function initializeRpcWebsocketClient() {
    const stop = logger.createLoading().start('Connecting to RPC websocket server...')

    await client.socket.connect().then(() => client.subscribeSlot()).then(() => client.subscribeLatency()).then(() => (isInitialized = true)).then(() => {
        stop('Connected to RPC websocket server!')
    })
}
