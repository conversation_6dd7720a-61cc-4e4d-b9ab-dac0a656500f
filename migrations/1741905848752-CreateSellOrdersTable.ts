import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateSellOrdersTable1741905848752 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "sell_orders" ("id" SERIAL NOT NULL, "mint" character varying NOT NULL, "config" json NOT NULL, "is_completed" boolean NOT NULL, "tp_count" integer NOT NULL, "tp" bigint NOT NULL, "sl" bigint NOT NULL, "hold_until" TIMESTAMP WITH TIME ZONE NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "buy_transaction_id" integer, CONSTRAINT "REL_bc2355e05015c9170b388e082b" UNIQUE ("buy_transaction_id"), CONSTRAINT "PK_4cd0bf5d8b4fb8ae50efaeddc23" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_c9fa123c9cd07cfd308971c950" ON "sell_orders" ("mint") `)
        await queryRunner.query(`ALTER TABLE "sell_orders" ADD CONSTRAINT "FK_bc2355e05015c9170b388e082bb" FOREIGN KEY ("buy_transaction_id") REFERENCES "buy_transactions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sell_orders" DROP CONSTRAINT "FK_bc2355e05015c9170b388e082bb"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_c9fa123c9cd07cfd308971c950"`)
        await queryRunner.query(`DROP TABLE "sell_orders"`)
    }
}
