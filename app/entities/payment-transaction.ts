import type { Token } from '../modules/indexer/types/entities/token'
import type { TokenAmount } from '../types/tracker'
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'
import { JsonColumn } from '../utils/database/columns/json-column'

@Entity()
export class PaymentTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column()
    public declare slot: number

    @Column()
    public declare signature: string

    @Index()
    @Column()
    public declare payer: string

    @Index()
    @Column()
    public declare recipient: string

    @JsonColumn()
    public declare amount: TokenAmount

    @Index()
    @Column()
    public declare datasource: string

    @Column('timestamptz')
    public declare receivedAt: Date

    @Column({ nullable: true })
    public declare blockTime?: number

    @Column({ nullable: true, type: 'timestamptz' })
    public declare nodeTime?: Date

    @JsonColumn()
    public declare tokens: Token[]

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date
}
