import type { BondingCurveAccount } from '../../../indexer/types/entities/bonding-curve'
import type { Trade } from '../../../indexer/types/entities/trade'
import type { AggregatedBondingCurve } from '../../types'
import { SYSTEM_PROGRAM_ADDRESS } from '@solana-program/system'

export const toBondingCurveAccount = (trade: Trade): BondingCurveAccount => ({
    signature: trade.signature,
    transactionIndex: trade.transactionIndex,
    eventIndex: trade.eventIndex,
    sortKey: trade.sortKey,
    slot: trade.slot,
    virtualSolReserves: trade.virtualSolReserves,
    virtualTokenReserves: trade.virtualTokenReserves,
    realSolReserves: trade.realSolReserves,
    realTokenReserves: trade.realTokenReserves,
    creator: trade.metadata?.creator ?? SYSTEM_PROGRAM_ADDRESS,
    feeBasisPoints: trade.metadata?.feeBasisPoints,
    creatorFeeBasisPoints: trade.metadata?.creatorFeeBasisPoints,
})

export const toAggregatedBondingCurve = (account: BondingCurveAccount): AggregatedBondingCurve => ({
    sortKey: account.sortKey,
    slot: account.slot,
    virtualSolReserves: account.virtualSolReserves,
    virtualTokenReserves: account.virtualTokenReserves,
    realSolReserves: account.realSolReserves,
    realTokenReserves: account.realTokenReserves,
    creator: account.creator,
    feeBasisPoints: account.feeBasisPoints,
    creatorFeeBasisPoints: account.creatorFeeBasisPoints,
})
