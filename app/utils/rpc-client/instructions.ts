import type { Base58EncodedBytes, IAccountMeta, IInstruction, IInstructionWithAccounts, IInstructionWithData } from '@solana/kit'
import type { GetTransactionResponse } from './transactions'
import { isString } from '@kdt310722/utils/string'
import base58 from 'bs58'

export type RawInstruction = Omit<GetTransactionResponse['transaction']['message']['instructions'][0], 'data'> & {
    data: Uint8Array | Base58EncodedBytes
}

export const parseInstruction = (accounts: IAccountMeta[], instruction: RawInstruction): IInstruction & IInstructionWithAccounts<IAccountMeta[]> & IInstructionWithData<Uint8Array> => ({
    accounts: instruction.accounts.map((index) => accounts[index]),
    data: isString(instruction.data) ? base58.decode(instruction.data) : instruction.data,
    programAddress: accounts[instruction.programIdIndex].address,
})
