import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateSellTransactionsTable1741913582545 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "sell_transactions" ("id" SERIAL NOT NULL, "mint" character varying NOT NULL, "token_info" json NOT NULL, "amount" bigint NOT NULL, "config" json NOT NULL, "sender" character varying NOT NULL, "slot" integer NOT NULL, "signature" character varying NOT NULL, "tokens_cost" bigint NOT NULL, "received_sol" bigint NOT NULL, "balance_before" bigint NOT NULL, "balance_after" bigint NOT NULL, "bonding_curve" json NOT NULL, "process_time" bigint NOT NULL, "send_time" bigint NOT NULL, "confirmation_time" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_fa93191005de6b025edc9728b30" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_7dd65a3f29ab2820d330474e3f" ON "sell_transactions" ("mint") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_7dd65a3f29ab2820d330474e3f"`)
        await queryRunner.query(`DROP TABLE "sell_transactions"`)
    }
}
