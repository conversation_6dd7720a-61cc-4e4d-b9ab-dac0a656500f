import type { Address, Signature, TransactionError } from '@solana/kit'

export interface ConfirmTransactionOptions {
    maxWaitTime?: number
    signal?: AbortSignal
}

export interface ConfirmTransactionResult {
    slot: number
    signature: string
    err: TransactionError | undefined
}

export interface TransactionLogs {
    signature: Signature
    err: TransactionError | null
    logs: string[] | null
}

export interface TransactionHeader {
    numRequiredSignatures: number
    numReadonlySignedAccounts: number
    numReadonlyUnsignedAccounts: number
}

export interface LoadedAddresses {
    writable: Address[]
    readonly: Address[]
}
