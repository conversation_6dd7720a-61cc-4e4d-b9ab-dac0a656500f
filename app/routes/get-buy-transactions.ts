import { avgBigint } from '@kdt310722/utils/array'
import { BigIntMath, formatNanoseconds } from '@kdt310722/utils/number'
import { buyTransactions } from '../common/buyer'

export const handler = () => {
    const total = Object.keys(buyTransactions).length
    const processTimes = Object.values(buyTransactions).map((tx) => tx.processTime)
    const minProcessTime = formatNanoseconds(processTimes.reduce((min, time) => BigIntMath.min(min, time), processTimes[0]))
    const maxProcessTime = formatNanoseconds(processTimes.reduce((max, time) => BigIntMath.max(max, time), processTimes[0]))
    const avgProcessTime = formatNanoseconds(avgBigint(processTimes))

    return { total, avgProcessTime, minProcessTime, maxProcessTime, transactions: buyTransactions }
}
