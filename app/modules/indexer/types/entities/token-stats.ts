export interface TokenStats {
    mint: string
    sortKey: bigint
    signature: string
    slot: number
    transactionIndex: number
    eventIndex: number
    atl: bigint
    ath: bigint
    buyVolume: bigint
    sellVolume: bigint
    buysCount: number
    sellsCount: number
    tradersCount: number
    buyersCount: number
    sellersCount: number
    lastTrade: Date
}

export type TokenStatsApiResponse = Omit<TokenStats, 'lastTrade'> & { lastTrade: string }
