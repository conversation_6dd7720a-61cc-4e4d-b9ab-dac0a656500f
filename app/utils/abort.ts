import { isNullish } from '@kdt310722/utils/common'

export function onAborted(handler: (error: unknown) => void, signal?: AbortSignal) {
    if (isNullish(signal)) {
        return () => {}
    }

    const handleAbort = () => {
        try {
            signal.throwIfAborted()
        } catch (error) {
            handler(error)
        }
    }

    signal.addEventListener('abort', handleAbort, { once: true })

    return () => {
        signal.removeEventListener('abort', handleAbort)
    }
}
