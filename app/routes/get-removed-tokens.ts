import type { Address } from '@solana/kit'
import { isArray } from '@kdt310722/utils/array'
import { filter, map } from '@kdt310722/utils/object'
import { z } from 'zod'
import { removedTokens } from '../common/cleaner'
import { address } from '../utils/schemas/address'

const addresses = address.array()

export const schema = z.union([addresses, z.object({ mints: addresses })]).default([]).transform((val) => {
    return isArray(val) ? { mints: val } : val
})

export const handler = ({ mints }: z.infer<typeof schema>) => {
    return map(filter(Object.fromEntries(removedTokens), (key) => mints.length === 0 || mints.includes(key as Address)), (k, v) => [k, v.join(',')])
}
