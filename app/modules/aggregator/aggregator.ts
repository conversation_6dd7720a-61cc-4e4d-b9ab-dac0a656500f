import type { BondingCurveAccount } from '../indexer/types/entities/bonding-curve'
import type { Token } from '../indexer/types/entities/token'
import type { TokenStats } from '../indexer/types/entities/token-stats'
import type { Trade } from '../indexer/types/entities/trade'
import type { Aggregated, AggregatedBondingCurve, AggregatedTokenStats } from './types'
import { LruSet } from '@kdt310722/utils/array'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { aggregate } from './utils/aggregate'
import { toAggregatedBondingCurve, toBondingCurveAccount } from './utils/formatters/to-aggregated-bonding-curve'
import { toAggregatedTokenStats } from './utils/formatters/to-aggregated-token-stats'
import { isNewTrade } from './utils/trades'

export type AggregatorEvents = {
    bondingCurve: (mint: string, bondingCurve: AggregatedBondingCurve) => void
    stats: (mint: string, stats: AggregatedTokenStats) => void
    removed: (mints: string[]) => void
}

export class Aggregator extends Emitter<AggregatorEvents, true> {
    public readonly items: Record<string, Aggregated> = {}

    protected readonly pendingTrades = new Set<Trade>()
    protected readonly ignoredTrades = new LruSet<Trade>(10_000)

    protected readonly handledTradeIds = new LruSet<string>(10_000)
    protected readonly ignoredTradeIds = new LruSet<string>(10_000)

    protected readonly mintsByCreator: Record<string, Set<string>> = {}
    protected readonly mintsByTrader: Record<string, Set<string>> = {}

    protected isStarted = false
    protected isResuming = false

    public get isReady() {
        return this.isStarted && !this.isResuming
    }

    public has(mint: string) {
        return mint in this.items
    }

    public get(mint: string): Aggregated | undefined {
        return this.items[mint]
    }

    public getTokensByAddress(address: string) {
        const createdTokens = this.mintsByCreator[address] ?? new Set()
        const tradingTokens = this.mintsByTrader[address] ?? new Set()

        return createdTokens.union(tradingTokens).values().map((mint) => this.get(mint)).filter(notNullish).toArray()
    }

    public setMetadata(mint: string, key: string, value: any) {
        if (this.has(mint)) {
            this.items[mint].metadata[key] = value
        }
    }

    public start() {
        return this.resume()
    }

    public pause() {
        if (this.isResuming) {
            throw new Error('Unable to pause while resuming')
        }

        this.isStarted = false
    }

    public resume() {
        this.isResuming = true
        this.isStarted = true

        for (const trade of this.pendingTrades) {
            this.handleTrade(trade)
            this.pendingTrades.delete(trade)
        }

        this.isResuming = false
    }

    public handleToken(token: Token) {
        this.items[token.mint] ??= { token, metadata: {} }
        this.mintsByCreator[token.createdBy] ??= new Set()
        this.mintsByCreator[token.createdBy].add(token.mint)

        for (const trade of this.ignoredTrades) {
            if (trade.mint === token.mint) {
                this.handledTradeIds.delete(this.getTradeId(trade))
                this.handleTrade(trade)
                this.ignoredTrades.delete(trade)
            }
        }
    }

    public handleGapTrade(trade: Trade) {
        this.handleTrade(trade, true)
    }

    public handleTrade(trade: Trade, force = false) {
        if (!this.isStarted && !force) {
            return tap(void 0, () => this.pendingTrades.add(trade))
        }

        const id = this.getTradeId(trade)

        if (this.handledTradeIds.has(id)) {
            return tap(void 0, () => this.ignoredTradeIds.add(id))
        }

        const item = tap(this.items[trade.mint], () => this.handledTradeIds.add(id))

        if (isNullish(item)) {
            return tap(void 0, () => this.ignoredTrades.add(trade))
        }

        this.addTrader(trade.mint, trade.user)

        if (isNullish(item.bondingCurve) || isNewTrade(item.bondingCurve, trade)) {
            this.handleBondingCurve(trade.mint, toBondingCurveAccount(trade))
        }

        if (notNullish(this.items[trade.mint])) {
            this.emit('stats', trade.mint, this.items[trade.mint].stats = aggregate(this.items[trade.mint], trade))
        }
    }

    public handleBondingCurve(mint: string, bondingCurve: BondingCurveAccount) {
        this.emit('bondingCurve', mint, this.items[mint].bondingCurve = toAggregatedBondingCurve(bondingCurve))
    }

    public handleTokenStats(mint: string, stats: TokenStats) {
        if (notNullish(this.items[mint]?.bondingCurve)) {
            this.emit('stats', mint, this.items[mint].stats = toAggregatedTokenStats(stats, this.items[mint].bondingCurve))
        }
    }

    public handleTraders(mint: string, traders: string[]) {
        for (const trader of traders) {
            this.addTrader(mint, trader)
        }
    }

    public remove(mints: string[]) {
        this.removeHandledTradeId(mints)
        this.removeIgnoredTradeId(mints)
        this.removeTraderToken(mints)
        this.removeIgnoredTrade(mints)

        for (const mint of mints) {
            const creator = this.items[mint]?.token.createdBy

            if (notNullish(creator)) {
                delete this.items[mint]
                this.removeCreatedToken(mint, creator)
            }
        }

        this.emit('removed', mints)
    }

    protected removeIgnoredTrade(mints: string[]) {
        for (const trade of this.ignoredTrades) {
            if (mints.includes(trade.mint)) {
                this.ignoredTrades.delete(trade)
            }
        }
    }

    protected removeIgnoredTradeId(mints: string[]) {
        for (const tradeId of this.ignoredTradeIds) {
            if (mints.some((mint) => tradeId.startsWith(mint))) {
                this.ignoredTradeIds.delete(tradeId)
            }
        }
    }

    protected removeHandledTradeId(mints: string[]) {
        for (const tradeId of this.handledTradeIds) {
            if (mints.some((mint) => tradeId.startsWith(mint))) {
                this.handledTradeIds.delete(tradeId)
            }
        }
    }

    protected removeCreatedToken(mint: string, creator: string) {
        this.mintsByCreator[creator]?.delete(mint)

        if (this.mintsByCreator[creator]?.size === 0) {
            delete this.mintsByCreator[creator]
        }
    }

    protected removeTraderToken(mints: string[]) {
        for (const trader of Object.keys(this.mintsByTrader)) {
            for (const mint of mints) {
                this.mintsByTrader[trader].delete(mint)
            }

            if (this.mintsByTrader[trader].size === 0) {
                delete this.mintsByTrader[trader]
            }
        }
    }

    protected addTrader(mint: string, trader: string) {
        this.mintsByTrader[trader] ??= new Set()
        this.mintsByTrader[trader].add(mint)
    }

    protected getTradeId(trade: Trade) {
        return `${trade.mint}:${trade.signature}-${trade.eventIndex}`
    }
}
