import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddUpdatedAtColumn1741785742033 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "buy_failed_transactions" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`)
        await queryRunner.query(`ALTER TABLE "buy_transactions" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "buy_transactions" DROP COLUMN "updated_at"`)
        await queryRunner.query(`ALTER TABLE "buy_failed_transactions" DROP COLUMN "updated_at"`)
    }
}
