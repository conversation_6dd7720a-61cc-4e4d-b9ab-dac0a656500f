import { z, type ZodBigInt, type ZodNumber } from 'zod'
import { isValidRange } from '../ranges'
import { nullish } from './nullish'

export const range = (type: ZodNumber | ZodBigInt) => {
    const typeSchema = nullish(type)
    const schema = z.object({ gt: typeSchema, gte: typeSchema, lt: typeSchema, lte: typeSchema })

    return schema.refine((val) => isValidRange(val), { message: 'Invalid range' })
}

export const bigIntRange = range(z.coerce.bigint())

export const numberRange = range(z.number())
