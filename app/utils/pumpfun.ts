import type { BondingCurveAccount } from '../modules/indexer/types/entities/bonding-curve'
import { calculateTokenPrice, calculateTokenPriceBefore, type CalculateTokenPriceBeforeParams } from '@kdt-sol/pumpfun-sdk'
import { PUMPFUN_TOKEN_DECIMALS, PUMPFUN_TOKEN_PRICE_DECIMALS } from '../constants'

export function getPumpFunTokenPrice({ virtualSolReserves, virtualTokenReserves }: Pick<BondingCurveAccount, 'virtualSolReserves' | 'virtualTokenReserves'>) {
    return calculateTokenPrice(virtualSolReserves, virtualTokenReserves, PUMPFUN_TOKEN_PRICE_DECIMALS)
}

export function getPumpFunTokenPriceBefore(params: Omit<CalculateTokenPriceBeforeParams, 'decimals'>) {
    return calculateTokenPriceBefore({ decimals: PUMPFUN_TOKEN_PRICE_DECIMALS, ...params })
}

export function getPumpFunMarketCap(price: bigint, tokenSupply: bigint, solPrice: number) {
    const denominator = Number((10n ** (PUMPFUN_TOKEN_PRICE_DECIMALS + (PUMPFUN_TOKEN_PRICE_DECIMALS - BigInt(PUMPFUN_TOKEN_DECIMALS)))))
    const supply = Number(tokenSupply / (10n ** BigInt(PUMPFUN_TOKEN_DECIMALS)))

    return Number(price) / Number(denominator) * supply * solPrice
}
