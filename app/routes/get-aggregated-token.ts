import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { aggregator } from '../common/aggregator'
import { address } from '../utils/schemas/address'

export const schema = z.union([address, z.object({ mint: address })]).transform((val) => {
    return isString(val) ? { mint: val } : val
})

export const handler = ({ mint }: z.infer<typeof schema>) => {
    return aggregator.get(mint)
}
