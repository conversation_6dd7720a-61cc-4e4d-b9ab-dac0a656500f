import type { DexScreenerPaymentTransaction } from '../../../types/tracker'
import type { Aggregated } from '../../aggregator/types'
import type { Token } from '../../indexer/types/entities/token'
import { escapeMarkdownV2 } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { transform } from '@kdt310722/utils/function'
import { format, formatNanoseconds } from '@kdt310722/utils/number'
import { formatDate } from '@kdt310722/utils/time'
import { Big } from 'big.js'
import { TOKEN_HELP_LINKS } from '../constants'
import { accountLink, link, tokenLink, txLink } from '../utils/messages'

export interface PaymentTransactionNotificationData {
    transaction: DexScreenerPaymentTransaction
    tokens: Aggregated[]
    availableToBuyTokens: Aggregated[]
    processTime: bigint
}

export function getTokenHelpLinks(mint: string) {
    const links = Object.entries(TOKEN_HELP_LINKS)
    const message: string[] = []

    for (const [label, url] of links) {
        message.push(link(url.replaceAll('__MINT__', mint), label))
    }

    return message.join(String.raw` \| `)
}

export function formatToken(token: Token) {
    return [`  \\- *${tokenLink(token.mint, token.symbol)}*`, ` \\- ${getTokenHelpLinks(token.mint)}`].join('')
}

export function formatTokens(tokens: Aggregated[]) {
    return tokens.map(({ token }) => formatToken(token)).join('\n')
}

export function getPaymentTransactionNotificationMessage({ transaction, tokens, availableToBuyTokens, processTime }: PaymentTransactionNotificationData) {
    const message: string[] = []

    message.push(
        `Transaction: *${txLink(transaction.signature)}*`,
        `Slot: *${transaction.slot}*`,
        `Source: *${escapeMarkdownV2(transaction.datasource)}*`,
        `Account: *${accountLink(transaction.payer)}*`,
        `Amount: *${escapeMarkdownV2(`${format(new Big(transaction.amount.amount.toString()).div(10 ** transaction.amount.decimals).toNumber())} ${transaction.amount.symbol}`)}*`,
        `Received At: *${escapeMarkdownV2(formatDate(new Date(transaction.receivedAt), true))}*`,
    )

    if (notNullish(transaction.blockTime)) {
        message.push(`Block Time: *${escapeMarkdownV2(formatDate(new Date(transaction.blockTime * 1000), true))}*`)
    }

    if (notNullish(transaction.nodeTime)) {
        message.push(
            `Node Time: *${escapeMarkdownV2(formatDate(new Date(transaction.nodeTime), true))}*`,
            `Latency: *${escapeMarkdownV2(transform(formatNanoseconds(BigInt((transaction.receivedAt - transaction.nodeTime) * 1e6)), (val) => (val.length === 0 ? '0ms' : val)))}*`,
        )
    }

    message.push(`Filter Time: *${escapeMarkdownV2(formatNanoseconds(processTime))}*`)

    if (tokens.length > 0) {
        message.push('Tokens:', formatTokens(tokens))
    }

    if (availableToBuyTokens.length > 0) {
        message.push('Available To Buy Tokens:', formatTokens(availableToBuyTokens))
    }

    return message.join('\n')
}
