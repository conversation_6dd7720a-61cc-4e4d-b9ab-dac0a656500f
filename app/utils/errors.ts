import { JsonRpcError } from '@kdt310722/rpc'
import { TransportHttpError } from '../errors/transport-http-error'

export function isAbortError(error: unknown): error is Error {
    return error instanceof Error && error.name === 'AbortError'
}

export function isTransportHttpError(error: unknown): error is TransportHttpError {
    return error instanceof TransportHttpError
}

export function isJsonRpcError(error: unknown): error is JsonRpcError {
    return error instanceof JsonRpcError
}

export function shouldRetry(error: unknown) {
    return !isAbortError(error) && !isTransportHttpError(error) && !isJsonRpcError(error)
}
