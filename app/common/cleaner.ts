import { highlight, message } from '@kdt310722/logger'
import { LruMap } from '@kdt310722/utils/object'
import { config } from '../config'
import { createChildLogger } from '../core/logger'
import { aggregator } from './aggregator'
import { filter } from './filter'
import { sellingTokens } from './seller'

export const removedTokens = new LruMap<string, string[]>(10_000)
export const logger = createChildLogger('common:cleaner')

filter.on('filter', (mint, current) => {
    if (current.some((filter) => config.cleaner.filters.includes(filter))) {
        if (sellingTokens.has(mint)) {
            return
        }

        aggregator.remove([mint])
        removedTokens.set(mint, current)
        logger.debug(message(() => `Removed token ${highlight(mint)}: ${current.map((i) => highlight(i)).join(', ')}`))
    }
})
