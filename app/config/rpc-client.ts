import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { nullish } from '../utils/schemas/nullish'
import { retry } from '../utils/schemas/retry'
import { httpUrl, wsUrl } from '../utils/schemas/urls'

const httpSchema = z.object({
    url: httpUrl,
    timeout: z.number().int().nonnegative().default(30_000),
    retry,
    headers: z.record(z.string(), z.string()).default({}),
    maxRequestPerSecond: z.number().min(1).int().default(100),
})

export const http = z.union([httpUrl, httpSchema]).transform((val) => {
    return isString(val) ? httpSchema.parse({ url: val }) : val
})

const timeout = z.object({
    connect: z.number().int().positive().default(5000),
    disconnect: z.number().int().positive().default(5000),
    request: z.number().int().positive().default(30_000),
})

const reconnect = z.object({
    enable: z.boolean().default(true),
    delay: z.number().int().nonnegative().default(1000),
    attempts: z.number().int().positive().default(5),
})

const heartbeat = z.object({
    enable: z.boolean().default(true),
    interval: z.number().int().positive().default(30_000),
    timeout: z.number().int().positive().default(10_000),
})

export const websocketSchema = z.object({
    url: wsUrl,
    protocols: nullish(z.union([z.string().nonempty(), z.string().nonempty().array()])),
    timeout: timeout.default({}).transform((val) => ({ connectTimeout: val.connect, disconnectTimeout: val.disconnect, sendTimeout: val.request, requestTimeout: val.request })),
    reconnect: reconnect.default({}),
    heartbeat: heartbeat.default({}),
    maxTimeBetweenTwoSlots: z.number().int().positive().default(5000),
})

export const websocket = z.union([wsUrl, websocketSchema]).transform((val) => {
    return isString(val) ? websocketSchema.parse({ url: val }) : val
})

export const rpcClient = z.object({ http, websocket })
