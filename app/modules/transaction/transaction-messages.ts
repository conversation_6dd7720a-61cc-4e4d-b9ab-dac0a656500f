import type { BlockhashLifetime, PriorityFee, TransactionMessage } from './types'
import { notNullish } from '@kdt310722/utils/common'
import { transform } from '@kdt310722/utils/function'
import { getSetComputeUnitLimitInstruction, getSetComputeUnitPriceInstruction } from '@solana-program/compute-budget'
import { AccountRole, type Address, address, appendTransactionMessageInstructions, type BaseTransactionMessage, createTransactionMessage, type IAccountMeta, type IInstruction, pipe, setTransactionMessageFeePayer, setTransactionMessageLifetimeUsingBlockhash } from '@solana/kit'
import { MICRO_LAMPORTS_PER_LAMPORT } from './constants'

export const JITO_DONT_FRONT_ACCOUNT: IAccountMeta = {
    address: address('jitodontfront111111111111111111111111111111'),
    role: AccountRole.READONLY,
}

export function setTransactionMessagePriorityFee<TTransaction extends BaseTransactionMessage>({ units, lamports, accounts = [] }: PriorityFee, tx: TTransaction): TTransaction {
    const microLamports = BigInt(Math.trunc(Number(lamports) / units * Number(MICRO_LAMPORTS_PER_LAMPORT)))
    const instructions = [transform(getSetComputeUnitLimitInstruction({ units }), (i) => ({ ...i, accounts: [...(i.accounts ?? []), ...accounts] })), getSetComputeUnitPriceInstruction({ microLamports })]

    return appendTransactionMessageInstructions(instructions, tx)
}

export interface BuildTransactionMessageParams {
    payer: Address
    blockhash: BlockhashLifetime
    instructions: IInstruction[]
    priorityFee?: PriorityFee
    antiMev?: boolean
}

export const buildTransactionMessage = ({ payer, blockhash, instructions, priorityFee, antiMev = true }: BuildTransactionMessageParams): TransactionMessage => pipe(
    createTransactionMessage({ version: 'legacy' }),
    (tx) => setTransactionMessageFeePayer(payer, tx),
    (tx) => setTransactionMessageLifetimeUsingBlockhash(blockhash, tx),
    (tx) => (notNullish(priorityFee) ? setTransactionMessagePriorityFee({ ...priorityFee, accounts: antiMev ? [JITO_DONT_FRONT_ACCOUNT] : [] }, tx) : tx),
    (tx) => appendTransactionMessageInstructions(instructions, tx),
)
