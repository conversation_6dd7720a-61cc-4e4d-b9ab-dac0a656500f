import type { Address } from '@solana/kit'
import type { WalletDatasourceTransaction } from '../modules/wallet/types'

export function getBalanceChange({ accounts, preBalances, postBalances }: WalletDatasourceTransaction, account: Address) {
    const index = accounts.findIndex((i) => i.address === account)

    if (index === -1) {
        throw new Error('Account does not exist')
    }

    const pre = preBalances[index]
    const post = postBalances[index]

    return { pre, post }
}
