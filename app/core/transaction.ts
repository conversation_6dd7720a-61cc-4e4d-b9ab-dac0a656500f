import type { Base64EncodedWireTransaction, Signature } from '@solana/kit'
import type { Sender } from '../modules/transaction/senders/sender'
import { highlight, message } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { tap, transform } from '@kdt310722/utils/function'
import { config } from '../config'
import { RequestStats } from '../entities/request-stats'
import { buildTransactionMessage, type BuildTransactionMessageParams } from '../modules/transaction/transaction-messages'
import { signAndSerializeTransaction } from '../modules/transaction/transactions'
import { type BlockhashLifetimeWithSlot, getBlockhashLifetime } from './block'
import { database } from './database'
import { createChildLogger } from './logger'
import { getWallet } from './wallet'

export interface MultipleSendersTransaction {
    lifetimeConstraint: BlockhashLifetimeWithSlot
    transactions: Array<{ senderId: number, serializedTransaction: Base64EncodedWireTransaction }>
}

export interface BuildMultipleSendersTransactionParams extends Omit<BuildTransactionMessageParams, 'blockhash' | 'payer'> {
    tip?: bigint
    senders?: number[]
}

export const senderIds = config.senders.map((_, i) => i)
export const primarySenderId = senderIds[0]

export async function buildMultipleSendersTransaction({ tip = 0n, senders = senderIds, ...params }: BuildMultipleSendersTransactionParams): Promise<MultipleSendersTransaction> {
    const signer = getWallet().signer
    const blockhash = getBlockhashLifetime()
    const transactionMessage = buildTransactionMessage({ ...params, blockhash, payer: signer.address })

    const transactions = await Promise.all(config.senders.map(async (sender, senderId) => {
        if (!senders.includes(senderId) || !sender.isValidTipAmount(tip)) {
            return null
        }

        return { senderId, serializedTransaction: await signAndSerializeTransaction(signer.keyPair, sender.appendSenderInstructions(signer, tip, transactionMessage)) }
    }))

    return { lifetimeConstraint: blockhash, transactions: transactions.filter(notNullish) }
}

const logger = createChildLogger('core:transaction')

export async function sendMultipleSendersTransaction(transaction: MultipleSendersTransaction, onSignature?: (signature: Signature, sender: Sender) => void) {
    const signatures: Signature[] = []
    const errors: unknown[] = []

    const handleSignature = (sender: Sender, signature: Signature) => {
        signatures.push(tap(signature, () => onSignature?.(signature, sender)))
    }

    const handleError = (sender: Sender, error: unknown) => {
        errors.push(error)
        logger.warn(`Failed to send transaction to sender ${highlight(sender.name)}`, error)
    }

    await Promise.all(transaction.transactions.map(async (i) => config.senders[i.senderId].sendToAllEndpoints(i.serializedTransaction).then((s) => handleSignature(config.senders[i.senderId], s)).catch((error) => handleError(config.senders[i.senderId], error))))

    if (signatures.length === 0) {
        throw new AggregateError(errors, 'Failed to send transaction to all senders')
    }

    return signatures
}

export async function initializeTransactionSenders() {
    const repository = transform(logger.info('Initialize transaction senders...'), () => database.getRepository(RequestStats))

    for (const sender of config.senders) {
        sender.on('beforeRequest', (endpoint, freeConnections) => {
            if (sender.isWarmupEnabled && freeConnections <= 0) {
                logger.error(`No free connections available for endpoint ${highlight(endpoint)} of sender ${highlight(sender.name)}`)
            }
        })

        sender.on('retry', (error) => logger.warn(`Retrying request to endpoint ${highlight(error.request.url)} of sender ${highlight(sender.name)}...`, error))
        sender.on('warmingUp', (connections, fromInterval) => logger.log(fromInterval ? 'debug' : 'info', message(() => `Warming up sender ${highlight(sender.name)} with ${highlight(connections)} connections...`)))
        sender.on('warmedUp', (connections, fromInterval) => logger.log(fromInterval ? 'debug' : 'info', `Warmed up sender ${highlight(sender.name)}, connections: ${Object.entries(connections).map(([name, count]) => `${highlight(name)}=${highlight(count)}`).join(', ')}`))
        sender.on('warmupFailed', (error) => logger.error(`Failed to warmup sender ${highlight(sender.name)}`, error))
        sender.on('connections', (endpoint, count) => logger.debug(message(() => `Current connections for endpoint ${highlight(endpoint)} of sender ${highlight(sender.name)}: ${highlight(count)}`)))

        sender.on('request', (endpoint, requestId, stats) => {
            repository.save({ sender: sender.name, endpoint, requestId, ...stats }).catch((error) => {
                logger.error(`Failed to save request stats for sender ${highlight(sender.name)}`, error, { endpoint, stats })
            })
        })

        await sender.startWarmupIfEnabled()
    }

    logger.info('All transaction senders are initialized!')
}

// TODO: Default warmup config for each sender
// TODO: auto warmup if no connection availables
// TODO: Diff group for buy success and buy failed
